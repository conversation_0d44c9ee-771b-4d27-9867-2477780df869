"use client"

import { Skeleton } from "@/components/ui/skeleton"
import {
  TableBody,
  TableCell,
  TableRow,
} from "@/components/ui/table"

interface SkeletonTableProps {
  columns: number
  rows: number
}

export function SkeletonTable({ columns, rows }: SkeletonTableProps) {
  return (
    <TableBody>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <TableRow key={rowIndex}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <TableCell key={colIndex}>
              <Skeleton className={colIndex === 0 ? "h-12 w-[250px]" : "h-4 w-[100px]"} />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </TableBody>
  )
}
