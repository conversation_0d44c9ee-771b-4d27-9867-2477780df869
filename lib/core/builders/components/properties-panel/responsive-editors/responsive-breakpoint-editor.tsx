'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'

interface ResponsiveBreakpointEditorProps {
  breakpoint: string
  title: string
  description: string
  responsive: any
  onUpdate: (breakpoint: string, updates: any) => void
}

export function ResponsiveBreakpointEditor({
  breakpoint,
  title,
  description,
  responsive,
  onUpdate
}: ResponsiveBreakpointEditorProps) {
  const breakpointData = responsive[breakpoint] || {}

  const updateBreakpoint = (updates: any) => {
    onUpdate(breakpoint, updates)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm">{title}</CardTitle>
        <CardDescription className="text-xs">
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Typography */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Typography</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Font Size</Label>
              <Input
                type="text"
                value={breakpointData.fontSize || ''}
                onChange={(e) => updateBreakpoint({ fontSize: e.target.value })}
                placeholder={breakpoint === 'mobile' ? '14px, 0.875rem' : breakpoint === 'tablet' ? '16px, 1rem' : '18px, 1.125rem'}
                className="text-xs mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Line Height</Label>
              <Input
                type="text"
                value={breakpointData.lineHeight || ''}
                onChange={(e) => updateBreakpoint({ lineHeight: e.target.value })}
                placeholder={breakpoint === 'mobile' ? '1.4, 20px' : breakpoint === 'tablet' ? '1.5, 24px' : '1.6, 28px'}
                className="text-xs mt-1"
              />
            </div>
          </div>
          <div>
            <Label className="text-xs">Text Align</Label>
            <select
              value={breakpointData.textAlign || ''}
              onChange={(e) => updateBreakpoint({ textAlign: e.target.value })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="">Inherit</option>
              <option value="left">Left</option>
              <option value="center">Center</option>
              <option value="right">Right</option>
            </select>
          </div>
        </div>

        <Separator />

        {/* Spacing */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Spacing</Label>
          <div>
            <Label className="text-xs mb-2 block">Padding</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Top"
                value={breakpointData.padding?.top || ''}
                onChange={(e) => updateBreakpoint({
                  padding: { ...breakpointData.padding, top: e.target.value }
                })}
                className="text-xs"
              />
              <Input
                placeholder="Right"
                value={breakpointData.padding?.right || ''}
                onChange={(e) => updateBreakpoint({
                  padding: { ...breakpointData.padding, right: e.target.value }
                })}
                className="text-xs"
              />
              <Input
                placeholder="Bottom"
                value={breakpointData.padding?.bottom || ''}
                onChange={(e) => updateBreakpoint({
                  padding: { ...breakpointData.padding, bottom: e.target.value }
                })}
                className="text-xs"
              />
              <Input
                placeholder="Left"
                value={breakpointData.padding?.left || ''}
                onChange={(e) => updateBreakpoint({
                  padding: { ...breakpointData.padding, left: e.target.value }
                })}
                className="text-xs"
              />
            </div>
          </div>
          <div>
            <Label className="text-xs mb-2 block">Margin</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Top"
                value={breakpointData.margin?.top || ''}
                onChange={(e) => updateBreakpoint({
                  margin: { ...breakpointData.margin, top: e.target.value }
                })}
                className="text-xs"
              />
              <Input
                placeholder="Right"
                value={breakpointData.margin?.right || ''}
                onChange={(e) => updateBreakpoint({
                  margin: { ...breakpointData.margin, right: e.target.value }
                })}
                className="text-xs"
              />
              <Input
                placeholder="Bottom"
                value={breakpointData.margin?.bottom || ''}
                onChange={(e) => updateBreakpoint({
                  margin: { ...breakpointData.margin, bottom: e.target.value }
                })}
                className="text-xs"
              />
              <Input
                placeholder="Left"
                value={breakpointData.margin?.left || ''}
                onChange={(e) => updateBreakpoint({
                  margin: { ...breakpointData.margin, left: e.target.value }
                })}
                className="text-xs"
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Layout */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Layout</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Width</Label>
              <Input
                type="text"
                value={breakpointData.width || ''}
                onChange={(e) => updateBreakpoint({ width: e.target.value })}
                placeholder={breakpoint === 'mobile' ? '100%, auto' : breakpoint === 'tablet' ? '100%, auto' : 'auto, 1200px'}
                className="text-xs mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Height</Label>
              <Input
                type="text"
                value={breakpointData.height || ''}
                onChange={(e) => updateBreakpoint({ height: e.target.value })}
                placeholder={breakpoint === 'mobile' ? 'auto, 200px' : breakpoint === 'tablet' ? 'auto, 300px' : 'auto, 400px'}
                className="text-xs mt-1"
              />
            </div>
          </div>
          <div>
            <Label className="text-xs">Display</Label>
            <select
              value={breakpointData.display || ''}
              onChange={(e) => updateBreakpoint({ display: e.target.value })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="">Inherit</option>
              <option value="block">Block</option>
              <option value="flex">Flex</option>
              <option value="grid">Grid</option>
              <option value="inline">Inline</option>
              <option value="inline-block">Inline Block</option>
              <option value="none">None</option>
            </select>
          </div>
          {breakpoint === 'desktop' && (
            <div>
              <Label className="text-xs">Max Width</Label>
              <Input
                type="text"
                value={breakpointData.maxWidth || ''}
                onChange={(e) => updateBreakpoint({ maxWidth: e.target.value })}
                placeholder="1200px, none"
                className="text-xs mt-1"
              />
            </div>
          )}
        </div>

        <Separator />

        {/* Flexbox (if display is flex) */}
        {breakpointData.display === 'flex' && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Flexbox</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Direction</Label>
                <select
                  value={breakpointData.flexDirection || ''}
                  onChange={(e) => updateBreakpoint({ flexDirection: e.target.value })}
                  className="w-full mt-1 px-2 py-1 text-xs border rounded"
                >
                  <option value="">Inherit</option>
                  <option value="row">Row</option>
                  <option value="column">Column</option>
                  <option value="row-reverse">Row Reverse</option>
                  <option value="column-reverse">Column Reverse</option>
                </select>
              </div>
              <div>
                <Label className="text-xs">Wrap</Label>
                <select
                  value={breakpointData.flexWrap || ''}
                  onChange={(e) => updateBreakpoint({ flexWrap: e.target.value })}
                  className="w-full mt-1 px-2 py-1 text-xs border rounded"
                >
                  <option value="">Inherit</option>
                  <option value="nowrap">No Wrap</option>
                  <option value="wrap">Wrap</option>
                  <option value="wrap-reverse">Wrap Reverse</option>
                </select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Justify Content</Label>
                <select
                  value={breakpointData.justifyContent || ''}
                  onChange={(e) => updateBreakpoint({ justifyContent: e.target.value })}
                  className="w-full mt-1 px-2 py-1 text-xs border rounded"
                >
                  <option value="">Inherit</option>
                  <option value="flex-start">Start</option>
                  <option value="center">Center</option>
                  <option value="flex-end">End</option>
                  <option value="space-between">Space Between</option>
                  <option value="space-around">Space Around</option>
                  <option value="space-evenly">Space Evenly</option>
                </select>
              </div>
              <div>
                <Label className="text-xs">Align Items</Label>
                <select
                  value={breakpointData.alignItems || ''}
                  onChange={(e) => updateBreakpoint({ alignItems: e.target.value })}
                  className="w-full mt-1 px-2 py-1 text-xs border rounded"
                >
                  <option value="">Inherit</option>
                  <option value="flex-start">Start</option>
                  <option value="center">Center</option>
                  <option value="flex-end">End</option>
                  <option value="stretch">Stretch</option>
                  <option value="baseline">Baseline</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
