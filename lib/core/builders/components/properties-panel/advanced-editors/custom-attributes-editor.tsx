'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Settings, 
  Plus, 
  X, 
  Code, 
  Tag,
  Database,
  CheckCircle,
  AlertTriangle,
  Copy,
  RotateCcw
} from 'lucide-react'

interface CustomAttributesEditorProps {
  block: any
  onUpdate: (updates: any) => void
}

export function CustomAttributesEditor({ block, onUpdate }: CustomAttributesEditorProps) {
  const customAttributes = block.customAttributes || {}
  const [jsonError, setJsonError] = useState<string | null>(null)

  const updateCustomAttributes = (updates: any) => {
    onUpdate({
      customAttributes: { ...customAttributes, ...updates }
    })
  }

  const handleDataAttributesChange = (value: string) => {
    try {
      const parsed = JSON.parse(value || '{}')
      setJsonError(null)
      updateCustomAttributes({ dataAttributes: parsed })
    } catch (error) {
      setJsonError('Invalid JSON format')
    }
  }

  const addDataAttribute = () => {
    const current = customAttributes.dataAttributes || {}
    const newKey = `data-custom-${Object.keys(current).length + 1}`
    updateCustomAttributes({
      dataAttributes: { ...current, [newKey]: '' }
    })
  }

  const removeDataAttribute = (key: string) => {
    const current = customAttributes.dataAttributes || {}
    const { [key]: removed, ...rest } = current
    updateCustomAttributes({ dataAttributes: rest })
  }

  const updateDataAttribute = (oldKey: string, newKey: string, value: string) => {
    const current = customAttributes.dataAttributes || {}
    const { [oldKey]: removed, ...rest } = current
    updateCustomAttributes({
      dataAttributes: { ...rest, [newKey]: value }
    })
  }

  const getAttributeCount = () => {
    let count = 0
    if (customAttributes.className) count++
    if (customAttributes.id) count++
    if (customAttributes.dataAttributes && Object.keys(customAttributes.dataAttributes).length > 0) {
      count += Object.keys(customAttributes.dataAttributes).length
    }
    return count
  }

  const attributeCount = getAttributeCount()

  const copyAttributesToClipboard = () => {
    const attributes = []
    if (customAttributes.className) {
      attributes.push(`class="${customAttributes.className}"`)
    }
    if (customAttributes.id) {
      attributes.push(`id="${customAttributes.id}"`)
    }
    if (customAttributes.dataAttributes) {
      Object.entries(customAttributes.dataAttributes).forEach(([key, value]) => {
        attributes.push(`${key}="${value}"`)
      })
    }
    navigator.clipboard.writeText(attributes.join(' '))
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm flex items-center">
              <Tag className="h-4 w-4 mr-2" />
              Custom HTML Attributes
            </CardTitle>
            <CardDescription className="text-xs">
              Add custom HTML attributes to the block element
            </CardDescription>
          </div>
          {attributeCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {attributeCount} attribute{attributeCount > 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* CSS Classes */}
        <div>
          <Label className="text-xs flex items-center mb-2">
            <Code className="h-3 w-3 mr-1" />
            CSS Classes
          </Label>
          <Input
            type="text"
            value={customAttributes.className || ''}
            onChange={(e) => updateCustomAttributes({ className: e.target.value })}
            placeholder="custom-class another-class"
            className="text-xs"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Space-separated CSS class names
          </p>
        </div>

        {/* HTML ID */}
        <div>
          <Label className="text-xs flex items-center mb-2">
            <Settings className="h-3 w-3 mr-1" />
            HTML ID
          </Label>
          <Input
            type="text"
            value={customAttributes.id || ''}
            onChange={(e) => updateCustomAttributes({ id: e.target.value })}
            placeholder="unique-element-id"
            className="text-xs"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Unique identifier for the element
          </p>
        </div>

        {/* Data Attributes */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs flex items-center">
              <Database className="h-3 w-3 mr-1" />
              Data Attributes
            </Label>
            <Button
              variant="outline"
              size="sm"
              onClick={addDataAttribute}
              className="text-xs h-6"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add
            </Button>
          </div>

          {/* Individual Data Attributes */}
          {customAttributes.dataAttributes && Object.entries(customAttributes.dataAttributes).map(([key, value]) => (
            <div key={key} className="flex items-center space-x-2 p-2 border rounded">
              <Input
                type="text"
                value={key}
                onChange={(e) => updateDataAttribute(key, e.target.value, value as string)}
                placeholder="data-attribute-name"
                className="text-xs flex-1"
              />
              <span className="text-xs text-muted-foreground">=</span>
              <Input
                type="text"
                value={value as string}
                onChange={(e) => updateDataAttribute(key, key, e.target.value)}
                placeholder="attribute value"
                className="text-xs flex-1"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeDataAttribute(key)}
                className="text-xs h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}

          {/* JSON Editor for Data Attributes */}
          <div className="space-y-2">
            <Label className="text-xs">Data Attributes (JSON)</Label>
            <Textarea
              value={JSON.stringify(customAttributes.dataAttributes || {}, null, 2)}
              onChange={(e) => handleDataAttributesChange(e.target.value)}
              placeholder='{"data-track": "click", "data-category": "hero"}'
              rows={4}
              className="font-mono text-xs"
            />
            {jsonError && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-xs">
                  JSON Error: {jsonError}
                </AlertDescription>
              </Alert>
            )}
            <p className="text-xs text-muted-foreground">
              Edit data attributes as JSON. Use "data-" prefix for attribute names.
            </p>
          </div>
        </div>

        {/* Custom Properties */}
        <div>
          <Label className="text-xs mb-2 block">Custom Properties</Label>
          <Textarea
            value={customAttributes.customProperties || ''}
            onChange={(e) => updateCustomAttributes({ customProperties: e.target.value })}
            placeholder="title=Custom Title&#10;role=button&#10;tabindex=0"
            rows={3}
            className="text-xs"
          />
          <p className="text-xs text-muted-foreground mt-1">
            One property per line in format: property=value
          </p>
        </div>

        {/* Preview */}
        {attributeCount > 0 && (
          <div className="space-y-2">
            <Label className="text-xs">Generated HTML Attributes</Label>
            <div className="bg-muted/50 rounded-md p-3">
              <code className="text-xs text-muted-foreground">
                &lt;div
                {customAttributes.className && (
                  <><br />  class="{customAttributes.className}"</>
                )}
                {customAttributes.id && (
                  <><br />  id="{customAttributes.id}"</>
                )}
                {customAttributes.dataAttributes && Object.entries(customAttributes.dataAttributes).map(([key, value]) => (
                  <span key={key}><br />  {key}="{String(value)}"</span>
                ))}
                {customAttributes.customProperties && customAttributes.customProperties.split('\n').filter(Boolean).map((prop: string, index: number) => {
                  const [key, ...valueParts] = prop.split('=')
                  const value = valueParts.join('=')
                  return key && value ? <span key={index}><br />  {key.trim()}="{value.trim()}"</span> : null
                })}
                <br />&gt;
              </code>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center space-x-2 pt-3 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={copyAttributesToClipboard}
            disabled={attributeCount === 0}
            className="text-xs"
          >
            <Copy className="h-3 w-3 mr-1" />
            Copy Attributes
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => updateCustomAttributes({})}
            className="text-xs"
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Clear All
          </Button>
        </div>

        {/* Common Data Attributes Templates */}
        <div className="space-y-2">
          <Label className="text-xs">Quick Templates</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateCustomAttributes({
                dataAttributes: {
                  ...customAttributes.dataAttributes,
                  'data-track': 'click',
                  'data-category': block.type
                }
              })}
              className="text-xs"
            >
              Analytics Tracking
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateCustomAttributes({
                dataAttributes: {
                  ...customAttributes.dataAttributes,
                  'data-aos': 'fade-up',
                  'data-aos-duration': '600'
                }
              })}
              className="text-xs"
            >
              AOS Animation
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateCustomAttributes({
                dataAttributes: {
                  ...customAttributes.dataAttributes,
                  'data-testid': `${block.type}-block`,
                  'data-cy': `${block.type}-${block.id}`
                }
              })}
              className="text-xs"
            >
              Testing IDs
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateCustomAttributes({
                dataAttributes: {
                  ...customAttributes.dataAttributes,
                  'data-theme': 'auto',
                  'data-color-mode': 'light'
                }
              })}
              className="text-xs"
            >
              Theme Attributes
            </Button>
          </div>
        </div>

        {/* Info */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5" />
            <div>
              <p className="text-xs font-medium text-blue-800 mb-1">Custom Attributes</p>
              <p className="text-xs text-blue-700">
                These attributes will be added to the block's HTML element and can be used for styling, JavaScript interactions, analytics tracking, and more.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
