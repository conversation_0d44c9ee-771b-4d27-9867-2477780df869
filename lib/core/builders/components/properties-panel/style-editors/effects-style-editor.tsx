'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'

interface EffectsStyleEditorProps {
  styling: any
  onUpdate: (updates: any) => void
}

export function EffectsStyleEditor({ styling, onUpdate }: EffectsStyleEditorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm">Border & Effects</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Border */}
        <div className="space-y-3">
          <div>
            <Label className="text-xs">Border Width</Label>
            <Input
              type="text"
              value={styling.borderWidth || ''}
              onChange={(e) => onUpdate({ borderWidth: e.target.value })}
              placeholder="1px, 2px 4px"
              className="text-xs mt-1"
            />
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Border Style</Label>
              <select
                value={styling.borderStyle || 'solid'}
                onChange={(e) => onUpdate({ borderStyle: e.target.value })}
                className="w-full mt-1 px-2 py-1 text-xs border rounded"
              >
                <option value="none">None</option>
                <option value="solid">Solid</option>
                <option value="dashed">Dashed</option>
                <option value="dotted">Dotted</option>
                <option value="double">Double</option>
                <option value="groove">Groove</option>
                <option value="ridge">Ridge</option>
                <option value="inset">Inset</option>
                <option value="outset">Outset</option>
              </select>
            </div>
            <div>
              <Label className="text-xs">Border Color</Label>
              <Input
                type="color"
                value={styling.borderColor || '#000000'}
                onChange={(e) => onUpdate({ borderColor: e.target.value })}
                className="h-8 mt-1"
              />
            </div>
          </div>
          <div>
            <Label className="text-xs">Border Radius</Label>
            <Input
              type="text"
              value={styling.borderRadius || ''}
              onChange={(e) => onUpdate({ borderRadius: e.target.value })}
              placeholder="8px, 50%"
              className="text-xs mt-1"
            />
          </div>
        </div>

        <Separator />

        {/* Shadow */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Switch
              checked={styling.enableShadow || false}
              onCheckedChange={(checked) => onUpdate({ enableShadow: checked })}
            />
            <Label className="text-xs">Enable Shadow</Label>
          </div>
          
          {styling.enableShadow && (
            <div className="space-y-3 ml-6">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Shadow Type</Label>
                  <select
                    value={styling.shadowType || 'drop'}
                    onChange={(e) => onUpdate({ shadowType: e.target.value })}
                    className="w-full mt-1 px-2 py-1 text-xs border rounded"
                  >
                    <option value="drop">Drop Shadow</option>
                    <option value="inner">Inner Shadow</option>
                  </select>
                </div>
                <div>
                  <Label className="text-xs">Shadow Color</Label>
                  <Input
                    type="color"
                    value={styling.shadowColor || '#000000'}
                    onChange={(e) => onUpdate({ shadowColor: e.target.value })}
                    className="h-8 mt-1"
                  />
                </div>
              </div>
              <div className="grid grid-cols-4 gap-1">
                <div>
                  <Label className="text-xs">X</Label>
                  <Input
                    type="number"
                    value={styling.shadowX || 0}
                    onChange={(e) => onUpdate({ shadowX: parseInt(e.target.value) })}
                    className="text-xs mt-1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Y</Label>
                  <Input
                    type="number"
                    value={styling.shadowY || 0}
                    onChange={(e) => onUpdate({ shadowY: parseInt(e.target.value) })}
                    className="text-xs mt-1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Blur</Label>
                  <Input
                    type="number"
                    value={styling.shadowBlur || 0}
                    onChange={(e) => onUpdate({ shadowBlur: parseInt(e.target.value) })}
                    min="0"
                    className="text-xs mt-1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Spread</Label>
                  <Input
                    type="number"
                    value={styling.shadowSpread || 0}
                    onChange={(e) => onUpdate({ shadowSpread: parseInt(e.target.value) })}
                    className="text-xs mt-1"
                  />
                </div>
              </div>
              <div>
                <Label className="text-xs">Opacity</Label>
                <Input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={styling.shadowOpacity || 0.25}
                  onChange={(e) => onUpdate({ shadowOpacity: parseFloat(e.target.value) })}
                  className="w-full mt-1"
                />
                <span className="text-xs text-muted-foreground">{styling.shadowOpacity || 0.25}</span>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Transform */}
        <div className="space-y-3">
          <Label className="text-xs">Transform</Label>
          <div className="grid grid-cols-3 gap-2">
            <div>
              <Label className="text-xs">Rotate</Label>
              <Input
                type="number"
                value={styling.rotate || 0}
                onChange={(e) => onUpdate({ rotate: parseInt(e.target.value) })}
                placeholder="0"
                className="text-xs mt-1"
              />
              <span className="text-xs text-muted-foreground">deg</span>
            </div>
            <div>
              <Label className="text-xs">Scale X</Label>
              <Input
                type="number"
                step="0.1"
                value={styling.scaleX || 1}
                onChange={(e) => onUpdate({ scaleX: parseFloat(e.target.value) })}
                placeholder="1"
                className="text-xs mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Scale Y</Label>
              <Input
                type="number"
                step="0.1"
                value={styling.scaleY || 1}
                onChange={(e) => onUpdate({ scaleY: parseFloat(e.target.value) })}
                placeholder="1"
                className="text-xs mt-1"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Translate X</Label>
              <Input
                type="text"
                value={styling.translateX || ''}
                onChange={(e) => onUpdate({ translateX: e.target.value })}
                placeholder="0px"
                className="text-xs mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Translate Y</Label>
              <Input
                type="text"
                value={styling.translateY || ''}
                onChange={(e) => onUpdate({ translateY: e.target.value })}
                placeholder="0px"
                className="text-xs mt-1"
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Opacity */}
        <div>
          <Label className="text-xs">Opacity</Label>
          <Input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={styling.opacity || 1}
            onChange={(e) => onUpdate({ opacity: parseFloat(e.target.value) })}
            className="w-full mt-1"
          />
          <span className="text-xs text-muted-foreground">{styling.opacity || 1}</span>
        </div>

        <Separator />

        {/* Filter Effects */}
        <div className="space-y-3">
          <Label className="text-xs">Filter Effects</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Blur</Label>
              <Input
                type="number"
                value={styling.blur || 0}
                onChange={(e) => onUpdate({ blur: parseInt(e.target.value) })}
                min="0"
                placeholder="0"
                className="text-xs mt-1"
              />
              <span className="text-xs text-muted-foreground">px</span>
            </div>
            <div>
              <Label className="text-xs">Brightness</Label>
              <Input
                type="number"
                step="0.1"
                value={styling.brightness || 1}
                onChange={(e) => onUpdate({ brightness: parseFloat(e.target.value) })}
                min="0"
                max="2"
                placeholder="1"
                className="text-xs mt-1"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Contrast</Label>
              <Input
                type="number"
                step="0.1"
                value={styling.contrast || 1}
                onChange={(e) => onUpdate({ contrast: parseFloat(e.target.value) })}
                min="0"
                max="2"
                placeholder="1"
                className="text-xs mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Saturate</Label>
              <Input
                type="number"
                step="0.1"
                value={styling.saturate || 1}
                onChange={(e) => onUpdate({ saturate: parseFloat(e.target.value) })}
                min="0"
                max="2"
                placeholder="1"
                className="text-xs mt-1"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
