'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'

interface BackgroundStyleEditorProps {
  styling: any
  onUpdate: (updates: any) => void
}

export function BackgroundStyleEditor({ styling, onUpdate }: BackgroundStyleEditorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm">Background</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label className="text-xs">Background Type</Label>
          <select
            value={styling.backgroundType || 'color'}
            onChange={(e) => onUpdate({ backgroundType: e.target.value })}
            className="w-full mt-1 px-2 py-1 text-xs border rounded"
          >
            <option value="color">Solid Color</option>
            <option value="gradient">Gradient</option>
            <option value="image">Image</option>
          </select>
        </div>

        {styling.backgroundType === 'color' && (
          <div>
            <Label className="text-xs">Background Color</Label>
            <div className="flex gap-2 mt-1">
              <Input
                type="color"
                value={styling.backgroundColor || '#ffffff'}
                onChange={(e) => onUpdate({ backgroundColor: e.target.value })}
                className="h-8 w-16"
              />
              <Input
                type="text"
                value={styling.backgroundColor || '#ffffff'}
                onChange={(e) => onUpdate({ backgroundColor: e.target.value })}
                placeholder="#ffffff"
                className="text-xs flex-1"
              />
            </div>
          </div>
        )}

        {styling.backgroundType === 'gradient' && (
          <div className="space-y-3">
            <div>
              <Label className="text-xs">Gradient Type</Label>
              <select
                value={styling.gradientType || 'linear'}
                onChange={(e) => onUpdate({ gradientType: e.target.value })}
                className="w-full mt-1 px-2 py-1 text-xs border rounded"
              >
                <option value="linear">Linear</option>
                <option value="radial">Radial</option>
              </select>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">From Color</Label>
                <Input
                  type="color"
                  value={styling.gradientFrom || '#ffffff'}
                  onChange={(e) => onUpdate({ gradientFrom: e.target.value })}
                  className="h-8 mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">To Color</Label>
                <Input
                  type="color"
                  value={styling.gradientTo || '#000000'}
                  onChange={(e) => onUpdate({ gradientTo: e.target.value })}
                  className="h-8 mt-1"
                />
              </div>
            </div>
            {styling.gradientType === 'linear' && (
              <div>
                <Label className="text-xs">Direction (degrees)</Label>
                <Input
                  type="number"
                  value={styling.gradientDirection || 0}
                  onChange={(e) => onUpdate({ gradientDirection: parseInt(e.target.value) })}
                  min="0"
                  max="360"
                  className="text-xs mt-1"
                />
              </div>
            )}
          </div>
        )}

        {styling.backgroundType === 'image' && (
          <div className="space-y-3">
            <div>
              <Label className="text-xs">Image URL</Label>
              <Input
                type="url"
                value={styling.backgroundImage || ''}
                onChange={(e) => onUpdate({ backgroundImage: e.target.value })}
                placeholder="https://example.com/image.jpg"
                className="text-xs mt-1"
              />
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Size</Label>
                <select
                  value={styling.backgroundSize || 'cover'}
                  onChange={(e) => onUpdate({ backgroundSize: e.target.value })}
                  className="w-full mt-1 px-2 py-1 text-xs border rounded"
                >
                  <option value="cover">Cover</option>
                  <option value="contain">Contain</option>
                  <option value="auto">Auto</option>
                </select>
              </div>
              <div>
                <Label className="text-xs">Position</Label>
                <select
                  value={styling.backgroundPosition || 'center'}
                  onChange={(e) => onUpdate({ backgroundPosition: e.target.value })}
                  className="w-full mt-1 px-2 py-1 text-xs border rounded"
                >
                  <option value="center">Center</option>
                  <option value="top">Top</option>
                  <option value="bottom">Bottom</option>
                  <option value="left">Left</option>
                  <option value="right">Right</option>
                </select>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={styling.backgroundRepeat || false}
                onCheckedChange={(checked) => onUpdate({ backgroundRepeat: checked })}
              />
              <Label className="text-xs">Repeat</Label>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
