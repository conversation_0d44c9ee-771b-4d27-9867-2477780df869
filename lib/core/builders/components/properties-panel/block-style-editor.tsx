'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'

import { BackgroundStyleEditor } from './style-editors/background-style-editor'
import { TypographyStyleEditor } from './style-editors/typography-style-editor'
import { SpacingStyleEditor } from './style-editors/spacing-style-editor'
import { EffectsStyleEditor } from './style-editors/effects-style-editor'

interface BlockStyleEditorProps {
  block: any
  onUpdate: (updates: any) => void
}

export function BlockStyleEditor({ block, onUpdate }: BlockStyleEditorProps) {
  const styling = block.styling || {}
  const [activeStyleTab, setActiveStyleTab] = useState('background')

  return (
    <div className="space-y-4">
      {/* Style Sub-tabs */}
      <Tabs value={activeStyleTab} onValueChange={setActiveStyleTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 text-xs">
          <TabsTrigger value="background">Background</TabsTrigger>
          <TabsTrigger value="typography">Typography</TabsTrigger>
          <TabsTrigger value="spacing">Spacing</TabsTrigger>
          <TabsTrigger value="effects">Effects</TabsTrigger>
        </TabsList>

        {/* Background Tab */}
        <TabsContent value="background" className="space-y-4 mt-4">
          <BackgroundStyleEditor styling={styling} onUpdate={onUpdate} />
        </TabsContent>

        {/* Typography Tab */}
        <TabsContent value="typography" className="space-y-4 mt-4">
          <TypographyStyleEditor styling={styling} onUpdate={onUpdate} />
        </TabsContent>

        {/* Spacing Tab */}
        <TabsContent value="spacing" className="space-y-4 mt-4">
          <SpacingStyleEditor styling={styling} onUpdate={onUpdate} />
        </TabsContent>

        {/* Effects Tab */}
        <TabsContent value="effects" className="space-y-4 mt-4">
          <EffectsStyleEditor styling={styling} onUpdate={onUpdate} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
