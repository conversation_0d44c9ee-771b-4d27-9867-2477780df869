'use client'

import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import {
  Type,
  Layout,
  Settings,
  Palette,
  Zap,
  Eye,
  Sparkles,
  Brain,
  Wand2,
  Lightbulb,
  MessageSquare,
  Send,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CustomFieldRenderer } from './custom-fields'
import { FieldConfig, FieldValue } from './custom-fields/types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'

interface DynamicBlockEditorProps {
  block: any
  blockType: any
  schema: any
  onUpdate: (updates: any) => void
}

export function DynamicBlockEditor({ block, blockType, schema, onUpdate }: DynamicBlockEditorProps) {
  const [activeSection, setActiveSection] = useState<string>('content')
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({})

  // AI-powered property suggestions state
  const [showAISuggestions, setShowAISuggestions] = useState(false)
  const [aiPrompt, setAiPrompt] = useState('')
  const [isGeneratingAI, setIsGeneratingAI] = useState(false)
  const [aiSuggestions, setAiSuggestions] = useState<any[]>([])
  const [showNaturalLanguageEditor, setShowNaturalLanguageEditor] = useState(false)

  if (!schema) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">No Configuration Available</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-muted-foreground">
            This block type doesn't have a configuration schema defined.
          </p>
        </CardContent>
      </Card>
    )
  }

  // Convert schema to field configurations
  const fieldConfigs = convertSchemaToFieldConfigs(schema)

  // Organize fields into sections
  const sections = organizeFieldsIntoSections(fieldConfigs)

  // Handle field value changes
  const handleFieldChange = (fieldId: string, value: FieldValue) => {
    onUpdate({ [fieldId]: value })
  }

  // Handle field validation with debouncing to prevent infinite loops
  const handleFieldValidation = useMemo(() => {
    const validationTimeouts = new Map<string, NodeJS.Timeout>()

    return (fieldId: string, isValid: boolean, message?: string) => {
      // Clear existing timeout for this field
      const existingTimeout = validationTimeouts.get(fieldId)
      if (existingTimeout) {
        clearTimeout(existingTimeout)
      }

      // Set new timeout to debounce validation updates
      const timeout = setTimeout(() => {
        setFieldErrors(prev => {
          const currentErrors = prev[fieldId] || []
          const newErrors = isValid ? [] : [message || 'Invalid value']

          // Only update if errors have actually changed
          if (JSON.stringify(currentErrors) !== JSON.stringify(newErrors)) {
            return {
              ...prev,
              [fieldId]: newErrors
            }
          }
          return prev
        })
        validationTimeouts.delete(fieldId)
      }, 100) // 100ms debounce

      validationTimeouts.set(fieldId, timeout)
    }
  }, [])

  // AI-powered property suggestions handlers
  const handleAIPropertySuggestion = async () => {
    if (!aiPrompt.trim()) return

    setIsGeneratingAI(true)
    try {
      // Mock AI suggestions - in production, this would call an AI service
      const mockSuggestions = [
        {
          field: 'title',
          value: 'AI-generated compelling headline',
          reason: 'Based on current trends and best practices'
        },
        {
          field: 'description',
          value: 'AI-optimized description text',
          reason: 'Optimized for engagement and clarity'
        },
        {
          field: 'buttonText',
          value: 'Shop Now',
          reason: 'High-converting call-to-action'
        }
      ]

      setAiSuggestions(mockSuggestions)
      toast.success('AI suggestions generated!')
    } catch (error) {
      toast.error('Failed to generate AI suggestions')
    } finally {
      setIsGeneratingAI(false)
    }
  }

  const handleApplyAISuggestion = (suggestion: any) => {
    onUpdate({ [suggestion.field]: suggestion.value })
    toast.success(`Applied AI suggestion for ${suggestion.field}`)
  }

  const handleNaturalLanguageEdit = async () => {
    if (!aiPrompt.trim()) return

    setIsGeneratingAI(true)
    try {
      // Mock natural language processing - in production, this would parse the prompt
      // and generate appropriate field updates
      const updates: any = {}

      if (aiPrompt.toLowerCase().includes('title') || aiPrompt.toLowerCase().includes('headline')) {
        updates.title = 'AI-generated title based on your request'
      }

      if (aiPrompt.toLowerCase().includes('description') || aiPrompt.toLowerCase().includes('text')) {
        updates.description = 'AI-generated description based on your request'
      }

      if (aiPrompt.toLowerCase().includes('button') || aiPrompt.toLowerCase().includes('cta')) {
        updates.buttonText = 'AI-generated CTA'
      }

      onUpdate(updates)
      setAiPrompt('')
      toast.success('Applied natural language edits!')
    } catch (error) {
      toast.error('Failed to process natural language edit')
    } finally {
      setIsGeneratingAI(false)
    }
  }

  return (
    <div className="space-y-4">
      {/* Block Info Header with AI Controls */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {blockType?.icon && (
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-sm">{blockType.icon}</span>
                </div>
              )}
              <div>
                <CardTitle className="text-sm font-semibold">
                  {blockType?.displayName || block.type}
                </CardTitle>
                {blockType?.description && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {blockType.description}
                  </p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                {blockType?.category || 'Block'}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowNaturalLanguageEditor(!showNaturalLanguageEditor)}
                className="text-xs h-7"
              >
                <Brain className="h-3 w-3 mr-1" />
                AI Edit
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* AI Natural Language Editor */}
        {showNaturalLanguageEditor && (
          <CardContent className="pt-0 pb-4">
            <div className="space-y-3 border-t pt-4">
              <div className="flex items-center gap-2 mb-2">
                <Sparkles className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">AI Property Editor</span>
              </div>

              <div className="space-y-2">
                <Textarea
                  value={aiPrompt}
                  onChange={(e) => setAiPrompt(e.target.value)}
                  placeholder="Describe how you want to modify this block's properties... e.g., 'Make the title more compelling' or 'Change button text to be more action-oriented'"
                  className="min-h-[60px] text-sm"
                  disabled={isGeneratingAI}
                />

                <div className="flex gap-2">
                  <Button
                    onClick={handleNaturalLanguageEdit}
                    disabled={isGeneratingAI || !aiPrompt.trim()}
                    size="sm"
                    className="flex-1"
                  >
                    {isGeneratingAI ? (
                      <>
                        <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-3 w-3 mr-2" />
                        Apply AI Edit
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={handleAIPropertySuggestion}
                    disabled={isGeneratingAI}
                    variant="outline"
                    size="sm"
                  >
                    <Lightbulb className="h-3 w-3 mr-1" />
                    Suggest
                  </Button>
                </div>
              </div>

              {/* AI Suggestions */}
              {aiSuggestions.length > 0 && (
                <div className="space-y-2 border-t pt-3">
                  <div className="flex items-center gap-2">
                    <Lightbulb className="h-3 w-3 text-yellow-500" />
                    <span className="text-xs font-medium">AI Suggestions</span>
                  </div>

                  {aiSuggestions.map((suggestion, index) => (
                    <div key={index} className="p-2 bg-gray-50 rounded-lg">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1">
                          <div className="text-xs font-medium text-gray-700 mb-1">
                            {suggestion.field}
                          </div>
                          <div className="text-xs text-gray-600 mb-1">
                            {suggestion.value}
                          </div>
                          <div className="text-xs text-gray-500">
                            {suggestion.reason}
                          </div>
                        </div>
                        <Button
                          onClick={() => handleApplyAISuggestion(suggestion)}
                          size="sm"
                          variant="outline"
                          className="text-xs h-6 px-2"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Dynamic Property Sections */}
      {sections.length > 1 ? (
        <Tabs value={activeSection} onValueChange={setActiveSection}>
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            {sections.map((section: any) => (
              <TabsTrigger key={section.id} value={section.id} className="text-xs">
                <section.icon className="h-3 w-3 mr-1" />
                {section.title}
              </TabsTrigger>
            ))}
          </TabsList>

          {sections.map((section: any) => (
            <TabsContent key={section.id} value={section.id} className="mt-4">
              <SimpleSectionRenderer
                title={section.title}
                description={section.description}
                icon={section.icon}
                fields={section.fields}
                values={block.configuration || {}}
                onChange={handleFieldChange}
                onValidate={handleFieldValidation}
                errors={fieldErrors}
              />
            </TabsContent>
          ))}
        </Tabs>
      ) : sections.length === 1 ? (
        <SimpleSectionRenderer
          title={(sections[0] as any).title}
          description={(sections[0] as any).description}
          icon={(sections[0] as any).icon}
          fields={(sections[0] as any).fields}
          values={block.configuration || {}}
          onChange={handleFieldChange}
          onValidate={handleFieldValidation}
          errors={fieldErrors}
        />
      ) : (
        <Card>
          <CardContent className="pt-6">
            <p className="text-sm text-muted-foreground text-center">
              No configuration fields available for this block.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Helper function to convert JSON schema to field configurations
function convertSchemaToFieldConfigs(schema: any): FieldConfig[] {
  const properties = schema.properties || {}
  const fieldConfigs: FieldConfig[] = []

  Object.entries(properties).forEach(([key, property]: [string, any]) => {
    const fieldConfig = convertPropertyToFieldConfig(key, property)
    if (fieldConfig) {
      fieldConfigs.push(fieldConfig)
    }
  })

  return fieldConfigs
}

// Helper function to convert a single property to field config
function convertPropertyToFieldConfig(key: string, property: any): FieldConfig | null {
  const baseConfig: Partial<FieldConfig> = {
    id: key,
    label: property.title || key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
    description: property.description,
    required: property.required || false,
    defaultValue: property.default,
    placeholder: property.placeholder
  }

  // Determine field type based on property type and x-component
  const component = property['x-component']
  const type = property.type

  if (component) {
    // Use custom component mapping
    const componentFieldTypeMap: Record<string, string> = {
      'textarea': 'textarea',
      'color-picker': 'color',
      'image-upload': 'image',
      'media-library': 'media',
      'array-field': 'repeater',
      'object-field': 'object',
      'rich-text': 'richtext',
      'code-editor': 'code',
      'link-field': 'link',
      'spacing-field': 'spacing',
      'typography-field': 'font',
      'responsive-field': 'object',
      'json-editor': 'code'
    }

    const fieldType = componentFieldTypeMap[component] || 'text'
    return {
      ...baseConfig,
      type: fieldType,
      componentProps: property['x-component-props'] || {}
    } as FieldConfig
  }

  // Map standard JSON schema types to field types
  switch (type) {
    case 'string':
      if (property.enum) {
        return {
          ...baseConfig,
          type: 'select',
          options: property.enum.map((value: string) => ({
            label: value.charAt(0).toUpperCase() + value.slice(1),
            value
          }))
        } as FieldConfig
      }
      return { ...baseConfig, type: 'text' } as FieldConfig

    case 'number':
    case 'integer':
      return {
        ...baseConfig,
        type: 'number',
        min: property.minimum,
        max: property.maximum,
        step: property.multipleOf || 1
      } as FieldConfig

    case 'boolean':
      return { ...baseConfig, type: 'boolean' } as FieldConfig

    case 'array':
      return {
        ...baseConfig,
        type: 'repeater',
        itemSchema: property.items
      } as FieldConfig

    case 'object':
      return {
        ...baseConfig,
        type: 'object',
        properties: property.properties
      } as FieldConfig

    default:
      return { ...baseConfig, type: 'text' } as FieldConfig
  }
}

// Helper function to organize fields into sections
function organizeFieldsIntoSections(fieldConfigs: FieldConfig[]) {
  // Define section categories
  const sectionCategories = {
    content: {
      id: 'content',
      title: 'Content',
      description: 'Text, media, and content settings',
      icon: Type,
      keywords: ['content', 'text', 'title', 'description', 'label', 'placeholder', 'value', 'src', 'alt', 'href']
    },
    layout: {
      id: 'layout',
      title: 'Layout',
      description: 'Size, spacing, and positioning',
      icon: Layout,
      keywords: ['width', 'height', 'size', 'alignment', 'position', 'spacing', 'margin', 'padding', 'gap']
    },
    style: {
      id: 'style',
      title: 'Style',
      description: 'Colors, borders, and visual styling',
      icon: Palette,
      keywords: ['color', 'background', 'border', 'shadow', 'variant', 'theme', 'gradient', 'opacity']
    },
    behavior: {
      id: 'behavior',
      title: 'Behavior',
      description: 'Interactions and animations',
      icon: Zap,
      keywords: ['action', 'click', 'hover', 'animation', 'transition', 'event', 'target', 'lazy']
    }
  }

  // Initialize sections
  const sections: any = {
    content: { ...sectionCategories.content, fields: [] },
    layout: { ...sectionCategories.layout, fields: [] },
    style: { ...sectionCategories.style, fields: [] },
    behavior: { ...sectionCategories.behavior, fields: [] }
  }

  // Categorize fields
  fieldConfigs.forEach((fieldConfig) => {
    const lowerLabel = (fieldConfig.label || fieldConfig.id).toLowerCase()
    const lowerDescription = (fieldConfig.description || '').toLowerCase()
    let assigned = false

    // Check which section this field belongs to
    for (const [sectionId, section] of Object.entries(sectionCategories)) {
      if (section.keywords.some(keyword =>
        lowerLabel.includes(keyword) || lowerDescription.includes(keyword)
      )) {
        sections[sectionId].fields.push(fieldConfig)
        assigned = true
        break
      }
    }

    // Default to content section if not categorized
    if (!assigned) {
      sections.content.fields.push(fieldConfig)
    }
  })

  // Return only sections that have fields
  return Object.values(sections).filter((section: any) =>
    section.fields.length > 0
  )
}

// Simple section renderer component for DynamicBlockEditor
interface SimpleSectionRendererProps {
  title?: string
  description?: string
  icon?: React.ComponentType<any>
  fields: FieldConfig[]
  values: Record<string, FieldValue>
  onChange: (fieldId: string, value: FieldValue) => void
  onValidate?: (fieldId: string, isValid: boolean, message?: string) => void
  errors?: Record<string, string[]>
}

function SimpleSectionRenderer({
  title,
  description,
  icon: Icon,
  fields,
  values,
  onChange,
  onValidate,
  errors
}: SimpleSectionRendererProps) {
  if (!fields || fields.length === 0) {
    return null
  }

  return (
    <Card>
      <CardContent className="pt-6">
        {(title || description) && (
          <div className="mb-4">
            {title && (
              <div className="flex items-center gap-2 mb-2">
                {Icon && <Icon className="h-4 w-4" />}
                <h3 className="text-sm font-semibold">{title}</h3>
              </div>
            )}
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
        )}

        <div className="space-y-4">
          {fields.map((field) => (
            <div key={field.id} className="space-y-2">
              <label className="text-xs font-medium text-gray-700 block">
                {field.label}
                {field.validation?.required && <span className="text-red-500 ml-1">*</span>}
              </label>

              {field.description && (
                <p className="text-xs text-gray-500">{field.description}</p>
              )}

              <CustomFieldRenderer
                config={field}
                value={values[field.id]}
                onChange={(value) => onChange(field.id, value)}
                onValidate={onValidate ? (isValid, message) => onValidate(field.id, isValid, message) : undefined}
                errors={errors?.[field.id]}
                className="w-full"
              />

              {errors?.[field.id] && errors[field.id].length > 0 && (
                <p className="text-xs text-red-500">{errors[field.id][0]}</p>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}