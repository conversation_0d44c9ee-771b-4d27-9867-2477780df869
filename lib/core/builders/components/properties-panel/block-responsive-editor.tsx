'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'

import { ResponsiveVisibilityControls } from './responsive-editors/responsive-visibility-controls'
import { ResponsiveBreakpointEditor } from './responsive-editors/responsive-breakpoint-editor'

interface BlockResponsiveEditorProps {
  block: any
  onUpdate: (updates: any) => void
}

export function BlockResponsiveEditor({ block, onUpdate }: BlockResponsiveEditorProps) {
  const responsive = block.responsive || {}
  const [activeBreakpoint, setActiveBreakpoint] = useState('mobile')

  const updateResponsiveStyle = (breakpoint: string, updates: any) => {
    onUpdate({
      [breakpoint]: { ...responsive[breakpoint], ...updates }
    })
  }

  return (
    <div className="space-y-4">
      {/* Visibility Controls */}
      <ResponsiveVisibilityControls
        responsive={responsive}
        onUpdate={onUpdate}
      />

      {/* Breakpoint Selector */}
      <Tabs value={activeBreakpoint} onValueChange={setActiveBreakpoint} className="w-full">
        <TabsList className="grid w-full grid-cols-3 text-xs">
          <TabsTrigger value="mobile">📱 Mobile</TabsTrigger>
          <TabsTrigger value="tablet">📱 Tablet</TabsTrigger>
          <TabsTrigger value="desktop">🖥️ Desktop</TabsTrigger>
        </TabsList>

        {/* Mobile Overrides */}
        <TabsContent value="mobile" className="space-y-4 mt-4">
          <ResponsiveBreakpointEditor
            breakpoint="mobile"
            title="Mobile Styles (≤ 768px)"
            description="Override styles specifically for mobile devices"
            responsive={responsive}
            onUpdate={updateResponsiveStyle}
          />
        </TabsContent>

        {/* Tablet Overrides */}
        <TabsContent value="tablet" className="space-y-4 mt-4">
          <ResponsiveBreakpointEditor
            breakpoint="tablet"
            title="Tablet Styles (769px - 1024px)"
            description="Override styles specifically for tablet devices"
            responsive={responsive}
            onUpdate={updateResponsiveStyle}
          />
        </TabsContent>

        {/* Desktop Overrides */}
        <TabsContent value="desktop" className="space-y-4 mt-4">
          <ResponsiveBreakpointEditor
            breakpoint="desktop"
            title="Desktop Styles (≥ 1025px)"
            description="Override styles specifically for desktop devices"
            responsive={responsive}
            onUpdate={updateResponsiveStyle}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
