'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Slider } from '@/components/ui/slider'
import {
  Zap,
  RotateCcw,
  AlertTriangle,
  Plus,
  Trash2,
  Copy,
  Settings,
  Layers,
  Sun
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface ShadowLayer {
  id: string
  x: number
  y: number
  blur: number
  spread: number
  color: string
  inset: boolean
  opacity: number
}

interface ShadowValue {
  layers: ShadowLayer[]
  enabled: boolean
}

export function ShadowField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<ShadowValue>({
    ...(value as ShadowValue || {}),
    layers: [
      {
        id: '1',
        x: 0,
        y: 4,
        blur: 8,
        spread: 0,
        color: '#00000040',
        inset: false,
        opacity: 0.25
      }
    ],
    enabled: true
  })
  const [selectedLayer, setSelectedLayer] = useState<string>('1')
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue({
      ...(value as ShadowValue),
      layers: [
        {
          id: '1',
          x: 0,
          y: 4,
          blur: 8,
          spread: 0,
          color: '#00000040',
          inset: false,
          opacity: 0.25
        }
      ],
      enabled: true
    })
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateShadow = (updates: Partial<ShadowValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const updateLayer = (layerId: string, updates: Partial<ShadowLayer>) => {
    const newLayers = localValue.layers.map(layer =>
      layer.id === layerId ? { ...layer, ...updates } : layer
    )
    updateShadow({ layers: newLayers })
  }

  const addLayer = () => {
    const newLayer: ShadowLayer = {
      id: Date.now().toString(),
      x: 0,
      y: 2,
      blur: 4,
      spread: 0,
      color: '#00000040',
      inset: false,
      opacity: 0.25
    }
    updateShadow({ layers: [...localValue.layers, newLayer] })
    setSelectedLayer(newLayer.id)
  }

  const removeLayer = (layerId: string) => {
    if (localValue.layers.length <= 1) return

    const newLayers = localValue.layers.filter(layer => layer.id !== layerId)
    updateShadow({ layers: newLayers })

    if (selectedLayer === layerId) {
      setSelectedLayer(newLayers[0]?.id || '')
    }
  }

  const duplicateLayer = (layerId: string) => {
    const layer = localValue.layers.find(l => l.id === layerId)
    if (!layer) return

    const newLayer: ShadowLayer = {
      ...layer,
      id: Date.now().toString(),
      x: layer.x + 2,
      y: layer.y + 2
    }
    updateShadow({ layers: [...localValue.layers, newLayer] })
    setSelectedLayer(newLayer.id)
  }

  const handleReset = () => {
    const defaultValue: ShadowValue = {
      ...(config.defaultValue as ShadowValue || {}),
      layers: (config.defaultValue as ShadowValue)?.layers || [
        {
          id: '1',
          x: 0,
          y: 4,
          blur: 8,
          spread: 0,
          color: '#00000040',
          inset: false,
          opacity: 0.25
        }
      ],
      enabled: (config.defaultValue as ShadowValue)?.enabled ?? true
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
    setSelectedLayer('1')
  }

  const generateShadowCSS = () => {
    if (!localValue.enabled || localValue.layers.length === 0) return 'none'

    return localValue.layers
      .map(layer => {
        const { x, y, blur, spread, color, inset } = layer
        const insetStr = inset ? 'inset ' : ''
        return `${insetStr}${x}px ${y}px ${blur}px ${spread}px ${color}`
      })
      .join(', ')
  }

  const selectedLayerData = localValue.layers.find(layer => layer.id === selectedLayer)
  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  // Generate shadow preview SVG
  const generateShadowPreview = () => {
    return (
      <svg width="120" height="80" className="mx-auto">
        <defs>
          <filter id={`shadow-filter-${config.id}`}>
            {localValue.layers.map((layer) => (
              <feDropShadow
                key={layer.id}
                dx={layer.x}
                dy={layer.y}
                stdDeviation={layer.blur / 2}
                floodColor={layer.color}
                floodOpacity={layer.opacity}
              />
            ))}
          </filter>

          <pattern id={`checker-${config.id}`} width="8" height="8" patternUnits="userSpaceOnUse">
            <rect width="4" height="4" fill="#f1f5f9" />
            <rect x="4" y="4" width="4" height="4" fill="#f1f5f9" />
            <rect x="4" y="0" width="4" height="4" fill="#e2e8f0" />
            <rect x="0" y="4" width="4" height="4" fill="#e2e8f0" />
          </pattern>
        </defs>

        {/* Background */}
        <rect width="120" height="80" fill={`url(#checker-${config.id})`} />

        {/* Shadow preview element */}
        <rect
          x="35"
          y="20"
          width="50"
          height="40"
          fill="#ffffff"
          stroke="#e2e8f0"
          strokeWidth="1"
          rx="8"
          filter={localValue.enabled ? `url(#shadow-filter-${config.id})` : 'none'}
          className="transition-all duration-200"
        />

        {/* Element content */}
        <circle cx="60" cy="40" r="8" fill="#3b82f6" opacity="0.6" />
        <text x="60" y="55" textAnchor="middle" className="text-xs fill-muted-foreground">
          Element
        </text>
      </svg>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Zap className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        {/* Controls */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => updateShadow({ enabled: !localValue.enabled })}
            className={cn(
              'h-5 w-5 p-0',
              localValue.enabled && 'text-primary'
            )}
            title={localValue.enabled ? 'Disable shadow' : 'Enable shadow'}
          >
            <Sun className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Shadow preview */}
      <div className="border rounded-lg p-4 bg-muted/20">
        {generateShadowPreview()}
      </div>

      {/* CSS output */}
      <div className="space-y-1">
        <Label className="text-xs">CSS Output</Label>
        <div className="bg-muted p-2 rounded text-xs font-mono break-all">
          box-shadow: {generateShadowCSS()};
        </div>
      </div>

      {/* Shadow controls */}
      <Tabs defaultValue="layers" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="layers" className="text-xs">
            <Layers className="h-3 w-3 mr-1" />
            Layers
          </TabsTrigger>
          <TabsTrigger value="settings" className="text-xs">
            <Settings className="h-3 w-3 mr-1" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="layers" className="space-y-3">
          {/* Layer list */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Shadow Layers</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={addLayer}
                className="h-6 px-2 text-xs"
                disabled={disabled}
              >
                <Plus className="h-3 w-3 mr-1" />
                Add
              </Button>
            </div>

            <div className="space-y-1">
              {localValue.layers.map((layer, index) => (
                <div
                  key={layer.id}
                  className={cn(
                    'flex items-center gap-2 p-2 border rounded cursor-pointer transition-colors',
                    selectedLayer === layer.id ? 'border-primary bg-primary/5' : 'border-border'
                  )}
                  onClick={() => setSelectedLayer(layer.id)}
                >
                  <div className="flex-1">
                    <div className="text-xs font-medium">
                      Layer {index + 1} {layer.inset && '(Inset)'}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {layer.x}px {layer.y}px {layer.blur}px {layer.spread}px
                    </div>
                  </div>

                  <div
                    className="w-4 h-4 rounded border"
                    style={{ backgroundColor: layer.color }}
                  />

                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        duplicateLayer(layer.id)
                      }}
                      className="h-5 w-5 p-0"
                      title="Duplicate layer"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        removeLayer(layer.id)
                      }}
                      className="h-5 w-5 p-0"
                      disabled={localValue.layers.length <= 1}
                      title="Remove layer"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-3">
          {selectedLayerData && (
            <>
              {/* Position controls */}
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <Label className="text-xs">X Offset</Label>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[selectedLayerData.x]}
                      onValueChange={([x]) => updateLayer(selectedLayer, { x })}
                      min={-20}
                      max={20}
                      step={1}
                      className="flex-1"
                      disabled={disabled}
                    />
                    <Badge variant="outline" className="text-xs h-4 px-1 min-w-[2rem]">
                      {selectedLayerData.x}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">Y Offset</Label>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[selectedLayerData.y]}
                      onValueChange={([y]) => updateLayer(selectedLayer, { y })}
                      min={-20}
                      max={20}
                      step={1}
                      className="flex-1"
                      disabled={disabled}
                    />
                    <Badge variant="outline" className="text-xs h-4 px-1 min-w-[2rem]">
                      {selectedLayerData.y}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Blur and spread */}
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <Label className="text-xs">Blur</Label>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[selectedLayerData.blur]}
                      onValueChange={([blur]) => updateLayer(selectedLayer, { blur })}
                      min={0}
                      max={50}
                      step={1}
                      className="flex-1"
                      disabled={disabled}
                    />
                    <Badge variant="outline" className="text-xs h-4 px-1 min-w-[2rem]">
                      {selectedLayerData.blur}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">Spread</Label>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[selectedLayerData.spread]}
                      onValueChange={([spread]) => updateLayer(selectedLayer, { spread })}
                      min={-20}
                      max={20}
                      step={1}
                      className="flex-1"
                      disabled={disabled}
                    />
                    <Badge variant="outline" className="text-xs h-4 px-1 min-w-[2rem]">
                      {selectedLayerData.spread}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Color and opacity */}
              <div className="space-y-2">
                <Label className="text-xs">Color</Label>
                <div className="flex gap-1">
                  <Input
                    type="color"
                    value={selectedLayerData.color}
                    onChange={(e) => updateLayer(selectedLayer, { color: e.target.value })}
                    className="w-8 h-6 p-0 border-0"
                    disabled={disabled}
                  />
                  <Input
                    type="text"
                    value={selectedLayerData.color}
                    onChange={(e) => updateLayer(selectedLayer, { color: e.target.value })}
                    className="text-xs h-6 flex-1"
                    disabled={disabled}
                  />
                </div>
              </div>

              {/* Inset toggle */}
              <div className="flex items-center justify-between">
                <Label className="text-xs">Inset Shadow</Label>
                <Button
                  variant={selectedLayerData.inset ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateLayer(selectedLayer, { inset: !selectedLayerData.inset })}
                  className="h-6 px-2 text-xs"
                  disabled={disabled}
                >
                  {selectedLayerData.inset ? 'Inset' : 'Drop'}
                </Button>
              </div>
            </>
          )}
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
