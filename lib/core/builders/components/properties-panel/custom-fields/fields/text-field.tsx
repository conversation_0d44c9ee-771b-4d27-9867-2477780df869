'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Eye, EyeOff, Copy, RotateCcw, AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

export function TextField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState(value as string || '')
  const [showPassword, setShowPassword] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)

  // Use ref to store the latest onValidate function to avoid dependency issues
  const onValidateRef = useRef(onValidate)
  onValidateRef.current = onValidate

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value as string || '')
  }, [value])

  // Validate field on value change with stable dependencies
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)

    // Use ref to avoid dependency on onValidate
    if (onValidateRef.current) {
      onValidateRef.current(result.isValid, result.message)
    }
  }, [localValue, config])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setLocalValue(newValue)
    onChange(newValue)
  }

  const handleClear = () => {
    setLocalValue('')
    onChange('')
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(localValue)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const isPassword = config.type === 'password'
  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium">
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        
        {/* Field actions */}
        <div className="flex items-center gap-1">
          {localValue && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopy}
                className="h-5 w-5 p-0"
                title="Copy value"
              >
                <Copy className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClear}
                className="h-5 w-5 p-0"
                title="Clear value"
              >
                <RotateCcw className="h-3 w-3" />
              </Button>
            </>
          )}
          
          {isPassword && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPassword(!showPassword)}
              className="h-5 w-5 p-0"
              title={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? (
                <EyeOff className="h-3 w-3" />
              ) : (
                <Eye className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Input field */}
      <div className="relative">
        <Input
          id={config.id}
          type={isPassword ? (showPassword ? 'text' : 'password') : 'text'}
          value={localValue}
          onChange={handleChange}
          placeholder={config.placeholder}
          disabled={disabled || config.disabled}
          className={cn(
            'text-xs',
            hasError && 'border-destructive focus-visible:ring-destructive',
            config.className
          )}
          maxLength={config.validation?.maxLength}
          minLength={config.validation?.minLength}
          pattern={config.validation?.pattern?.source}
          required={config.validation?.required}
        />
        
        {/* Character count */}
        {config.validation?.maxLength && (
          <div className="absolute right-2 top-1/2 -translate-y-1/2">
            <Badge
              variant={localValue.length > config.validation.maxLength * 0.8 ? 'destructive' : 'secondary'}
              className="text-xs h-4 px-1"
            >
              {localValue.length}/{config.validation.maxLength}
            </Badge>
          </div>
        )}
      </div>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Field hints */}
      {config.validation?.pattern && !hasError && (
        <p className="text-xs text-muted-foreground">
          Format: {config.validation.pattern.source}
        </p>
      )}
    </div>
  )
}

// Specialized text field variants
export function EmailField(props: CustomFieldProps) {
  return (
    <TextField
      {...props}
      config={{
        ...props.config,
        type: 'email',
        validation: {
          ...props.config.validation,
          pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        }
      }}
    />
  )
}

export function UrlField(props: CustomFieldProps) {
  return (
    <TextField
      {...props}
      config={{
        ...props.config,
        type: 'url',
        validation: {
          ...props.config.validation,
          pattern: /^https?:\/\/.+/
        }
      }}
    />
  )
}

export function PasswordField(props: CustomFieldProps) {
  return (
    <TextField
      {...props}
      config={{
        ...props.config,
        type: 'password'
      }}
    />
  )
}

export function PhoneField(props: CustomFieldProps) {
  return (
    <TextField
      {...props}
      config={{
        ...props.config,
        type: 'tel',
        validation: {
          ...props.config.validation,
          pattern: /^[\+]?[1-9][\d]{0,15}$/
        }
      }}
    />
  )
}
