'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Slider } from '@/components/ui/slider'
import {
  Play,
  RotateCcw,
  AlertTriangle,
  Pause,
  Square,
  Zap,
  Settings,
  Timer,
  Repeat
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface AnimationValue {
  name: string
  duration: number
  delay: number
  iterationCount: number | 'infinite'
  direction: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse'
  fillMode: 'none' | 'forwards' | 'backwards' | 'both'
  timingFunction: string
  playState: 'running' | 'paused'
}

export function AnimationField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<AnimationValue>({
    name: 'fadeIn',
    duration: 1000,
    delay: 0,
    iterationCount: 1,
    direction: 'normal',
    fillMode: 'forwards',
    timingFunction: 'ease',
    playState: 'paused',
    ...(value as AnimationValue || {})
  })
  const [isPlaying, setIsPlaying] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue({
      name: 'fadeIn',
      duration: 1000,
      delay: 0,
      iterationCount: 1,
      direction: 'normal',
      fillMode: 'forwards',
      timingFunction: 'ease',
      playState: 'paused',
      ...(value as AnimationValue || {})
    })
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateAnimation = (updates: Partial<AnimationValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: AnimationValue = {
      name: 'fadeIn',
      duration: 1000,
      delay: 0,
      iterationCount: 1,
      direction: 'normal',
      fillMode: 'forwards',
      timingFunction: 'ease',
      playState: 'paused',
      ...(config.defaultValue as AnimationValue || {})
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
    setIsPlaying(false)
  }

  const togglePlayback = () => {
    const newPlayState = isPlaying ? 'paused' : 'running'
    setIsPlaying(!isPlaying)
    updateAnimation({ playState: newPlayState })
  }

  const stopAnimation = () => {
    setIsPlaying(false)
    updateAnimation({ playState: 'paused' })
  }

  // Animation presets
  const animationPresets = [
    { name: 'fadeIn', label: 'Fade In' },
    { name: 'fadeOut', label: 'Fade Out' },
    { name: 'slideInLeft', label: 'Slide In Left' },
    { name: 'slideInRight', label: 'Slide In Right' },
    { name: 'slideInUp', label: 'Slide In Up' },
    { name: 'slideInDown', label: 'Slide In Down' },
    { name: 'zoomIn', label: 'Zoom In' },
    { name: 'zoomOut', label: 'Zoom Out' },
    { name: 'rotateIn', label: 'Rotate In' },
    { name: 'bounce', label: 'Bounce' },
    { name: 'pulse', label: 'Pulse' },
    { name: 'shake', label: 'Shake' },
    { name: 'wobble', label: 'Wobble' },
    { name: 'swing', label: 'Swing' },
    { name: 'flip', label: 'Flip' }
  ]

  const timingFunctions = [
    { value: 'ease', label: 'Ease' },
    { value: 'ease-in', label: 'Ease In' },
    { value: 'ease-out', label: 'Ease Out' },
    { value: 'ease-in-out', label: 'Ease In Out' },
    { value: 'linear', label: 'Linear' },
    { value: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', label: 'Custom' }
  ]

  const directions = [
    { value: 'normal', label: 'Normal' },
    { value: 'reverse', label: 'Reverse' },
    { value: 'alternate', label: 'Alternate' },
    { value: 'alternate-reverse', label: 'Alt Reverse' }
  ]

  const fillModes = [
    { value: 'none', label: 'None' },
    { value: 'forwards', label: 'Forwards' },
    { value: 'backwards', label: 'Backwards' },
    { value: 'both', label: 'Both' }
  ]

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  // Generate animation preview SVG
  const generateAnimationPreview = () => {
    const animationDuration = `${localValue.duration}ms`
    const animationDelay = `${localValue.delay}ms`

    return (
      <svg width="120" height="80" className="mx-auto">
        <defs>
          <linearGradient id={`anim-gradient-${config.id}`} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="100%" stopColor="#8b5cf6" />
          </linearGradient>
        </defs>

        {/* Background */}
        <rect width="120" height="80" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1" rx="4" />

        {/* Animated element */}
        <g>
          <circle
            cx="60"
            cy="40"
            r="12"
            fill={`url(#anim-gradient-${config.id})`}
            className={cn(
              'transition-all',
              isPlaying && getAnimationClass(localValue.name)
            )}
            style={{
              animationDuration,
              animationDelay,
              animationIterationCount: localValue.iterationCount === 'infinite' ? 'infinite' : localValue.iterationCount,
              animationDirection: localValue.direction,
              animationFillMode: localValue.fillMode,
              animationTimingFunction: localValue.timingFunction,
              animationPlayState: localValue.playState
            }}
          />

          {/* Animation name label */}
          <text x="60" y="65" textAnchor="middle" className="text-xs fill-muted-foreground">
            {localValue.name}
          </text>
        </g>

        {/* Play state indicator */}
        <g transform="translate(100, 10)">
          {isPlaying ? (
            <circle cx="0" cy="0" r="3" fill="#10b981" className="animate-pulse" />
          ) : (
            <circle cx="0" cy="0" r="3" fill="#6b7280" />
          )}
        </g>

        {/* Timeline indicator */}
        <g transform="translate(10, 70)">
          <rect width="100" height="2" fill="#e2e8f0" rx="1" />
          {isPlaying && (
            <rect
              width="100"
              height="2"
              fill="#3b82f6"
              rx="1"
              className="animate-pulse"
              style={{
                animationDuration,
                animationIterationCount: localValue.iterationCount === 'infinite' ? 'infinite' : localValue.iterationCount
              }}
            />
          )}
        </g>
      </svg>
    )
  }

  const getAnimationClass = (animationName: string) => {
    // Map animation names to CSS classes
    const animationMap: Record<string, string> = {
      fadeIn: 'animate-fade-in',
      fadeOut: 'animate-fade-out',
      slideInLeft: 'animate-slide-in-left',
      slideInRight: 'animate-slide-in-right',
      slideInUp: 'animate-slide-in-up',
      slideInDown: 'animate-slide-in-down',
      zoomIn: 'animate-zoom-in',
      zoomOut: 'animate-zoom-out',
      rotateIn: 'animate-rotate-in',
      bounce: 'animate-bounce',
      pulse: 'animate-pulse',
      shake: 'animate-shake',
      wobble: 'animate-wobble',
      swing: 'animate-swing',
      flip: 'animate-flip'
    }
    return animationMap[animationName] || 'animate-pulse'
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Play className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        {/* Controls */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={togglePlayback}
            className={cn(
              'h-5 w-5 p-0',
              isPlaying && 'text-primary'
            )}
            title={isPlaying ? 'Pause animation' : 'Play animation'}
          >
            {isPlaying ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={stopAnimation}
            className="h-5 w-5 p-0"
            title="Stop animation"
          >
            <Square className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Animation preview */}
      <div className="border rounded-lg p-4 bg-muted/20">
        {generateAnimationPreview()}
      </div>

      {/* Animation controls */}
      <Tabs defaultValue="presets" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="presets" className="text-xs">
            <Zap className="h-3 w-3 mr-1" />
            Presets
          </TabsTrigger>
          <TabsTrigger value="timing" className="text-xs">
            <Timer className="h-3 w-3 mr-1" />
            Timing
          </TabsTrigger>
          <TabsTrigger value="settings" className="text-xs">
            <Settings className="h-3 w-3 mr-1" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="presets" className="space-y-3">
          {/* Animation presets */}
          <div className="space-y-2">
            <Label className="text-xs">Animation Type</Label>
            <div className="grid grid-cols-2 gap-1">
              {animationPresets.slice(0, 8).map((preset) => (
                <Button
                  key={preset.name}
                  variant={localValue.name === preset.name ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateAnimation({ name: preset.name })}
                  className="text-xs h-6"
                  disabled={disabled}
                >
                  {preset.label}
                </Button>
              ))}
            </div>
          </div>

          {/* More presets */}
          <div className="grid grid-cols-2 gap-1">
            {animationPresets.slice(8).map((preset) => (
              <Button
                key={preset.name}
                variant={localValue.name === preset.name ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateAnimation({ name: preset.name })}
                className="text-xs h-6"
                disabled={disabled}
              >
                {preset.label}
              </Button>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="timing" className="space-y-3">
          {/* Duration */}
          <div className="space-y-2">
            <Label className="text-xs">Duration</Label>
            <div className="flex items-center gap-2">
              <Slider
                value={[localValue.duration]}
                onValueChange={([duration]) => updateAnimation({ duration })}
                min={100}
                max={5000}
                step={100}
                className="flex-1"
                disabled={disabled}
              />
              <Badge variant="outline" className="text-xs h-4 px-1 min-w-[3rem]">
                {localValue.duration}ms
              </Badge>
            </div>
          </div>

          {/* Delay */}
          <div className="space-y-2">
            <Label className="text-xs">Delay</Label>
            <div className="flex items-center gap-2">
              <Slider
                value={[localValue.delay]}
                onValueChange={([delay]) => updateAnimation({ delay })}
                min={0}
                max={2000}
                step={100}
                className="flex-1"
                disabled={disabled}
              />
              <Badge variant="outline" className="text-xs h-4 px-1 min-w-[3rem]">
                {localValue.delay}ms
              </Badge>
            </div>
          </div>

          {/* Timing function */}
          <div className="space-y-2">
            <Label className="text-xs">Easing</Label>
            <div className="grid grid-cols-2 gap-1">
              {timingFunctions.map((func) => (
                <Button
                  key={func.value}
                  variant={localValue.timingFunction === func.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateAnimation({ timingFunction: func.value })}
                  className="text-xs h-6"
                  disabled={disabled}
                >
                  {func.label}
                </Button>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-3">
          {/* Iteration count */}
          <div className="space-y-2">
            <Label className="text-xs">Repeat</Label>
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={localValue.iterationCount === 'infinite' ? '' : localValue.iterationCount}
                onChange={(e) => {
                  const value = e.target.value
                  updateAnimation({
                    iterationCount: value === '' ? 'infinite' : Number(value)
                  })
                }}
                placeholder="infinite"
                min={1}
                className="text-xs h-6 flex-1"
                disabled={disabled}
              />
              <Button
                variant={localValue.iterationCount === 'infinite' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateAnimation({ iterationCount: 'infinite' })}
                className="text-xs h-6"
                disabled={disabled}
              >
                <Repeat className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Direction */}
          <div className="space-y-2">
            <Label className="text-xs">Direction</Label>
            <div className="grid grid-cols-2 gap-1">
              {directions.map((direction) => (
                <Button
                  key={direction.value}
                  variant={localValue.direction === direction.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateAnimation({ direction: direction.value as any })}
                  className="text-xs h-6"
                  disabled={disabled}
                >
                  {direction.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Fill mode */}
          <div className="space-y-2">
            <Label className="text-xs">Fill Mode</Label>
            <div className="grid grid-cols-2 gap-1">
              {fillModes.map((mode) => (
                <Button
                  key={mode.value}
                  variant={localValue.fillMode === mode.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateAnimation({ fillMode: mode.value as any })}
                  className="text-xs h-6"
                  disabled={disabled}
                >
                  {mode.label}
                </Button>
              ))}
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* CSS output */}
      <div className="space-y-1">
        <Label className="text-xs">CSS Output</Label>
        <div className="bg-muted p-2 rounded text-xs font-mono break-all">
          animation: {localValue.name} {localValue.duration}ms {localValue.timingFunction} {localValue.delay}ms {localValue.iterationCount} {localValue.direction} {localValue.fillMode} {localValue.playState};
        </div>
      </div>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
