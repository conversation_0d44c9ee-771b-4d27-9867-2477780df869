'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  Image,
  RotateCcw,
  AlertTriangle,
  Plus,
  X,
  Eye,
  Upload,
  Play,
  Volume2,
  FileText
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'
import { MediaFile, MediaLibrary } from '@/lib/appwrite/media'
import { MediaLibraryModal } from '../components/media-library-modal'

interface MediaValue {
  files: MediaFile[]
  multiple: boolean
}

export function MediaField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<MediaValue>({
    ...(value as MediaValue || {}),
    files: (value as MediaValue)?.files || (value as MediaFile ? [value as MediaFile] : []),
    multiple: (value as MediaValue)?.multiple || false
  })
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)

  useEffect(() => {
    setLocalValue({
      ...(value as MediaValue || {}),
      files: (value as MediaValue)?.files || (value as MediaFile ? [value as MediaFile] : []),
      multiple: (value as MediaValue)?.multiple || false
    })
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateMedia = (updates: Partial<MediaValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: MediaValue = {
      ...(config.defaultValue as MediaValue || {}),
      files: (config.defaultValue as MediaValue)?.files || [],
      multiple: (config.defaultValue as MediaValue)?.multiple || false
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const handleMediaSelect = (selectedFiles: MediaFile | MediaFile[]) => {
    const files = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles]
    updateMedia({ files })
    setIsModalOpen(false)
  }

  const removeFile = (fileId: string) => {
    const newFiles = localValue.files.filter(file => file.$id !== fileId)
    updateMedia({ files: newFiles })
  }

  const openMediaLibrary = () => {
    setIsModalOpen(true)
  }

  // Get configuration options
  const multiple = (config as any).multiple || false
  const accept = (config as any).accept || []
  const maxFiles = (config as any).maxFiles || (multiple ? 10 : 1)

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])
  const hasFiles = localValue.files.length > 0

  // Generate media preview
  const generateMediaPreview = () => {
    if (!hasFiles) {
      return (
        <div className="border-2 border-dashed rounded-lg p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 mx-auto bg-muted rounded-lg flex items-center justify-center">
              <Image className="h-8 w-8 text-muted-foreground" />
            </div>
            <div>
              <p className="font-medium mb-1">No media selected</p>
              <p className="text-sm text-muted-foreground mb-4">
                Choose files from your media library
              </p>
              <Button
                onClick={openMediaLibrary}
                disabled={disabled}
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Select Media
              </Button>
            </div>
          </div>
        </div>
      )
    }

    if (multiple) {
      return (
        <div className="space-y-3">
          {/* Selected files grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {localValue.files.map((file) => (
              <MediaFilePreview
                key={file.$id}
                file={file}
                onRemove={() => removeFile(file.$id)}
                disabled={disabled}
              />
            ))}

            {/* Add more button */}
            {localValue.files.length < maxFiles && (
              <button
                onClick={openMediaLibrary}
                disabled={disabled}
                className={cn(
                  'aspect-square border-2 border-dashed rounded-lg flex flex-col items-center justify-center gap-2 transition-colors hover:bg-muted/50',
                  disabled && 'opacity-50 cursor-not-allowed'
                )}
              >
                <Plus className="h-6 w-6 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Add Media</span>
              </button>
            )}
          </div>
        </div>
      )
    }

    // Single file preview
    const file = localValue.files[0]
    return (
      <div className="space-y-3">
        <MediaFilePreview
          file={file}
          onRemove={() => removeFile(file.$id)}
          disabled={disabled}
          size="large"
        />

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={openMediaLibrary}
            disabled={disabled}
            className="flex-1"
          >
            <Upload className="h-4 w-4 mr-2" />
            Change Media
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(file.url, '_blank')}
            disabled={disabled}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Image className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <div className="flex items-center gap-1">
          {hasFiles && (
            <Badge variant="outline" className="text-xs h-4 px-1">
              {localValue.files.length} file{localValue.files.length !== 1 ? 's' : ''}
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Media preview */}
      {generateMediaPreview()}

      {/* File info */}
      {hasFiles && (
        <div className="space-y-2">
          {localValue.files.map((file) => (
            <div key={file.$id} className="text-xs text-muted-foreground space-y-1">
              <div className="flex items-center justify-between">
                <span className="font-medium">{file.name}</span>
                <span>{MediaLibrary.formatFileSize(file.sizeOriginal)}</span>
              </div>
              {file.metadata?.width && file.metadata?.height && (
                <div>Dimensions: {file.metadata.width} × {file.metadata.height}</div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Media Library Modal */}
      <MediaLibraryModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSelect={handleMediaSelect}
        multiple={multiple}
        accept={accept}
        title="Select Media"
      />
    </div>
  )
}

interface MediaFilePreviewProps {
  file: MediaFile
  onRemove: () => void
  disabled?: boolean
  size?: 'small' | 'large'
}

function MediaFilePreview({ file, onRemove, disabled, size = 'small' }: MediaFilePreviewProps) {
  const isImage = file.mimeType.startsWith('image/')
  const isVideo = file.mimeType.startsWith('video/')
  const isAudio = file.mimeType.startsWith('audio/')

  const sizeClasses = size === 'large'
    ? 'aspect-video max-h-48'
    : 'aspect-square'

  return (
    <div className={cn('relative group border rounded-lg overflow-hidden', sizeClasses)}>
      {/* File preview */}
      <div className="w-full h-full bg-muted flex items-center justify-center">
        {isImage && file.preview ? (
          <img
            src={file.preview}
            alt={file.metadata?.alt || file.name}
            className="w-full h-full object-cover"
          />
        ) : isVideo ? (
          <div className="flex flex-col items-center gap-2 text-muted-foreground">
            <Play className="h-8 w-8" />
            <span className="text-xs text-center px-2">{file.name}</span>
          </div>
        ) : isAudio ? (
          <div className="flex flex-col items-center gap-2 text-muted-foreground">
            <Volume2 className="h-8 w-8" />
            <span className="text-xs text-center px-2">{file.name}</span>
          </div>
        ) : (
          <div className="flex flex-col items-center gap-2 text-muted-foreground">
            <FileText className="h-8 w-8" />
            <span className="text-xs text-center px-2">{file.name}</span>
          </div>
        )}
      </div>

      {/* Remove button */}
      <Button
        variant="destructive"
        size="sm"
        onClick={onRemove}
        disabled={disabled}
        className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <X className="h-3 w-3" />
      </Button>

      {/* File type indicator */}
      <div className="absolute bottom-2 left-2">
        <Badge variant="secondary" className="text-xs h-4 px-1">
          {file.mimeType.split('/')[1]?.toUpperCase()}
        </Badge>
      </div>
    </div>
  )
}
