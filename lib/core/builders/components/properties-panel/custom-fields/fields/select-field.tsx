'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command'
import { 
  Check, 
  ChevronDown, 
  X, 
  Search, 
  RotateCcw, 
  AlertTriangle,
  Plus
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

export function SelectField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState(value)
  const [isOpen, setIsOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value)
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const handleSelect = (selectedValue: any) => {
    setLocalValue(selectedValue)
    onChange(selectedValue)
    setIsOpen(false)
    setSearchValue('')
  }

  const handleClear = () => {
    setLocalValue(null)
    onChange(null)
  }

  const handleReset = () => {
    const defaultValue = config.defaultValue
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const handleCreateOption = () => {
    if (config.creatable && searchValue.trim()) {
      const newOption = {
        label: searchValue.trim(),
        value: searchValue.trim()
      }
      
      // Add to options if not already present
      if (!config.options?.find(opt => opt.value === newOption.value)) {
        config.options = [...(config.options || []), newOption]
      }
      
      handleSelect(newOption.value)
    }
  }

  const options = config.options || []
  const filteredOptions = config.searchable && searchValue
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchValue.toLowerCase()) ||
        String(option.value).toLowerCase().includes(searchValue.toLowerCase())
      )
    : options

  const selectedOption = options.find(option => option.value === localValue)
  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium">
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        
        {/* Field actions */}
        <div className="flex items-center gap-1">
          {config.clearable && localValue && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="h-5 w-5 p-0"
              title="Clear selection"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Select dropdown */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isOpen}
            className={cn(
              'w-full justify-between text-xs h-8',
              !selectedOption && 'text-muted-foreground',
              hasError && 'border-destructive',
              config.className
            )}
            disabled={disabled || config.disabled}
          >
            <div className="flex items-center gap-2 truncate">
              {selectedOption?.icon}
              <span className="truncate">
                {selectedOption ? selectedOption.label : (config.placeholder || 'Select an option...')}
              </span>
            </div>
            <ChevronDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            {config.searchable && (
              <div className="flex items-center border-b px-3">
                <Search className="mr-2 h-3 w-3 shrink-0 opacity-50" />
                <Input
                  placeholder={`Search ${config.label.toLowerCase()}...`}
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="border-0 focus-visible:ring-0 text-xs h-8"
                />
              </div>
            )}
            
            <CommandEmpty className="py-6 text-center text-xs">
              {config.creatable && searchValue ? (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCreateOption}
                  className="text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Create "{searchValue}"
                </Button>
              ) : (
                'No options found.'
              )}
            </CommandEmpty>
            
            <CommandGroup className="max-h-48 overflow-auto">
              {filteredOptions.map((option) => (
                <CommandItem
                  key={String(option.value)}
                  value={String(option.value)}
                  onSelect={() => handleSelect(option.value)}
                  className="text-xs"
                >
                  <div className="flex items-center gap-2 flex-1">
                    {option.icon}
                    <div className="flex-1">
                      <div className="font-medium">{option.label}</div>
                      {option.description && (
                        <div className="text-xs text-muted-foreground">
                          {option.description}
                        </div>
                      )}
                    </div>
                  </div>
                  <Check
                    className={cn(
                      'ml-auto h-3 w-3',
                      localValue === option.value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selected value display */}
      {selectedOption && config.showValue && (
        <div className="text-xs text-muted-foreground">
          Selected: <code className="bg-muted px-1 rounded">{String(selectedOption.value)}</code>
        </div>
      )}

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Quick options */}
      {config.quickOptions && (
        <div className="flex flex-wrap gap-1">
          {config.quickOptions.map((option: any) => (
            <Button
              key={String(option.value)}
              variant={localValue === option.value ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSelect(option.value)}
              className="h-6 px-2 text-xs"
            >
              {option.icon}
              {option.label}
            </Button>
          ))}
        </div>
      )}
    </div>
  )
}

// Helper function to create select options
export function createSelectOptions(
  items: Array<string | { label: string; value: any; icon?: React.ReactNode; description?: string }>
) {
  return items.map(item => {
    if (typeof item === 'string') {
      return { label: item, value: item }
    }
    return item
  })
}

// Predefined select field variants
export function FontFamilySelectField(props: CustomFieldProps) {
  const fontOptions = createSelectOptions([
    'Inter',
    'P22 Underground',
    'Arial',
    'Helvetica',
    'Times New Roman',
    'Georgia',
    'Verdana',
    'Courier New',
    'monospace'
  ])

  return (
    <SelectField
      {...props}
      config={{
        ...props.config,
        options: fontOptions,
        searchable: true
      }}
    />
  )
}

export function FontWeightSelectField(props: CustomFieldProps) {
  const weightOptions = createSelectOptions([
    { label: 'Thin (100)', value: '100' },
    { label: 'Extra Light (200)', value: '200' },
    { label: 'Light (300)', value: '300' },
    { label: 'Normal (400)', value: '400' },
    { label: 'Medium (500)', value: '500' },
    { label: 'Semibold (600)', value: '600' },
    { label: 'Bold (700)', value: '700' },
    { label: 'Extra Bold (800)', value: '800' },
    { label: 'Black (900)', value: '900' }
  ])

  return (
    <SelectField
      {...props}
      config={{
        ...props.config,
        options: weightOptions
      }}
    />
  )
}
