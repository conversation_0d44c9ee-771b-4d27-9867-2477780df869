'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  Repeat,
  RotateCcw,
  AlertTriangle,
  Plus,
  Trash2,
  GripVertical,
  Copy,
  ChevronDown,
  ChevronRight,
  List
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface RepeaterItem {
  id: string
  data: Record<string, any>
  collapsed?: boolean
}

interface RepeaterValue {
  items: RepeaterItem[]
}

export function RepeaterField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<RepeaterValue>({
    ...(value as RepeaterValue || {}),
    items: (value as RepeaterValue)?.items || []
  })
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  const [validationError, setValidationError] = useState<string | null>(null)

  useEffect(() => {
    setLocalValue({
      ...(value as RepeaterValue || {}),
      items: (value as RepeaterValue)?.items || []
    })
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateRepeater = (updates: Partial<RepeaterValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const addItem = () => {
    const newItem: RepeaterItem = {
      id: Date.now().toString(),
      data: {},
      collapsed: false
    }
    updateRepeater({
      items: [...localValue.items, newItem]
    })
  }

  const removeItem = (index: number) => {
    const newItems = localValue.items.filter((_, i) => i !== index)
    updateRepeater({ items: newItems })
  }

  const duplicateItem = (index: number) => {
    const item = localValue.items[index]
    const newItem: RepeaterItem = {
      id: Date.now().toString(),
      data: { ...item.data },
      collapsed: false
    }
    const newItems = [...localValue.items]
    newItems.splice(index + 1, 0, newItem)
    updateRepeater({ items: newItems })
  }

  const toggleCollapse = (index: number) => {
    const newItems = [...localValue.items]
    newItems[index] = {
      ...newItems[index],
      collapsed: !newItems[index].collapsed
    }
    updateRepeater({ items: newItems })
  }

  const moveItem = (fromIndex: number, toIndex: number) => {
    const newItems = [...localValue.items]
    const [movedItem] = newItems.splice(fromIndex, 1)
    newItems.splice(toIndex, 0, movedItem)
    updateRepeater({ items: newItems })
  }

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    if (draggedIndex !== null && draggedIndex !== dropIndex) {
      moveItem(draggedIndex, dropIndex)
    }
    setDraggedIndex(null)
  }

  const handleReset = () => {
    const defaultValue: RepeaterValue = {
      ...(config.defaultValue as RepeaterValue || {}),
      items: (config.defaultValue as RepeaterValue)?.items || []
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  // Get field schema from config
  const fieldSchema = (config as any).fields || []
  const maxItems = (config as any).maxItems || 10
  const minItems = (config as any).minItems || 0

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])
  const canAddMore = localValue.items.length < maxItems
  const canRemove = localValue.items.length > minItems

  // Generate repeater preview SVG
  const generateRepeaterPreview = () => {
    const itemCount = localValue.items.length

    return (
      <svg width="100%" height="60" className="border rounded bg-muted/20">
        <defs>
          <pattern id={`repeater-grid-${config.id}`} width="10" height="10" patternUnits="userSpaceOnUse">
            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f1f5f9" strokeWidth="0.5"/>
          </pattern>
        </defs>

        {/* Background */}
        <rect width="100%" height="100%" fill={`url(#repeater-grid-${config.id})`} />

        {/* Items visualization */}
        {itemCount > 0 ? (
          <g>
            {Array.from({ length: Math.min(itemCount, 5) }).map((_, i) => (
              <g key={i}>
                <rect
                  x={10 + i * 25}
                  y={15}
                  width={20}
                  height={30}
                  fill="#3b82f6"
                  opacity={0.6 - i * 0.1}
                  rx="2"
                />
                <text
                  x={20 + i * 25}
                  y={32}
                  textAnchor="middle"
                  className="text-xs fill-white"
                >
                  {i + 1}
                </text>
              </g>
            ))}

            {itemCount > 5 && (
              <text x={150} y={32} className="text-xs fill-muted-foreground">
                +{itemCount - 5} more
              </text>
            )}
          </g>
        ) : (
          <g>
            <rect x="50%" y="20" width="40" height="20" fill="#e2e8f0" rx="2" transform="translate(-20, 0)" />
            <text x="50%" y="50" textAnchor="middle" className="text-xs fill-muted-foreground">
              No items
            </text>
          </g>
        )}
      </svg>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Repeat className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs h-4 px-1">
            {localValue.items.length} items
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Repeater preview */}
      <div className="space-y-2">
        {generateRepeaterPreview()}
      </div>

      {/* Add item button */}
      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          size="sm"
          onClick={addItem}
          disabled={!canAddMore || disabled}
          className="text-xs h-6"
        >
          <Plus className="h-3 w-3 mr-1" />
          Add Item
        </Button>

        {maxItems > 0 && (
          <span className="text-xs text-muted-foreground">
            {localValue.items.length} / {maxItems}
          </span>
        )}
      </div>

      {/* Repeater items */}
      <div className="space-y-2">
        {localValue.items.map((item, index) => (
          <div
            key={item.id}
            draggable
            onDragStart={(e) => handleDragStart(e, index)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, index)}
            className={cn(
              'border rounded-lg bg-background transition-all',
              draggedIndex === index && 'opacity-50',
              'hover:shadow-sm'
            )}
          >
            {/* Item header */}
            <div className="flex items-center justify-between p-2 border-b bg-muted/20">
              <div className="flex items-center gap-2">
                <GripVertical className="h-3 w-3 text-muted-foreground cursor-grab" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleCollapse(index)}
                  className="h-4 w-4 p-0"
                >
                  {item.collapsed ? (
                    <ChevronRight className="h-3 w-3" />
                  ) : (
                    <ChevronDown className="h-3 w-3" />
                  )}
                </Button>
                <span className="text-xs font-medium">
                  Item {index + 1}
                </span>
              </div>

              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => duplicateItem(index)}
                  className="h-4 w-4 p-0"
                  title="Duplicate item"
                  disabled={!canAddMore || disabled}
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem(index)}
                  className="h-4 w-4 p-0"
                  title="Remove item"
                  disabled={!canRemove || disabled}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>

            {/* Item content */}
            {!item.collapsed && (
              <div className="p-3 space-y-3">
                {fieldSchema.length > 0 ? (
                  <div className="text-xs text-muted-foreground text-center py-4">
                    Field schema would be rendered here
                  </div>
                ) : (
                  <div className="text-xs text-muted-foreground text-center py-4">
                    No field schema defined for this repeater
                  </div>
                )}
              </div>
            )}
          </div>
        ))}

        {localValue.items.length === 0 && (
          <div className="border-2 border-dashed rounded-lg p-6 text-center">
            <List className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm font-medium mb-1">No items yet</p>
            <p className="text-xs text-muted-foreground mb-3">
              Click "Add Item" to create your first item
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={addItem}
              disabled={disabled}
              className="text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add First Item
            </Button>
          </div>
        )}
      </div>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
