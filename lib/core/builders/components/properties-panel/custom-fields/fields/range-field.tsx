'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { RotateCcw, AlertTriangle, Zap, Target } from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

export function RangeField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState(Number(value) || 0)
  const [validationError, setValidationError] = useState<string | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isHovering, setIsHovering] = useState(false)
  const sliderRef = useRef<SVGSVGElement>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(Number(value) || 0)
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const min = config.min || 0
  const max = config.max || 100
  const step = config.step || 1
  const unit = config.unit || ''
  const showValue = config.showValue !== false
  const marks = config.marks || []

  const percentage = ((localValue - min) / (max - min)) * 100
  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  const handleMouseDown = (e: React.MouseEvent) => {
    if (disabled || config.disabled) return
    setIsDragging(true)
    updateValueFromMouse(e)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return
    updateValueFromMouse(e)
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const updateValueFromMouse = (e: React.MouseEvent) => {
    if (!sliderRef.current) return

    const rect = sliderRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
    const newValue = min + (percentage / 100) * (max - min)
    const steppedValue = Math.round(newValue / step) * step

    setLocalValue(steppedValue)
    onChange(steppedValue)
  }

  const handleReset = () => {
    const defaultValue = Number(config.defaultValue) || 0
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  // Generate gradient colors based on value
  const getGradientColor = () => {
    const hue = (percentage / 100) * 120 // 0 = red, 120 = green
    return `hsl(${hue}, 70%, 50%)`
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Zap className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        {/* Reset button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          className="h-5 w-5 p-0"
          title="Reset to default"
        >
          <RotateCcw className="h-3 w-3" />
        </Button>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Custom SVG Slider */}
      <div className="space-y-2">
        <div className="relative">
          <svg
            ref={sliderRef}
            width="100%"
            height="40"
            className="cursor-pointer"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            {/* Background track */}
            <defs>
              <linearGradient id={`track-gradient-${config.id}`} x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#e2e8f0" />
                <stop offset="100%" stopColor="#cbd5e1" />
              </linearGradient>

              {/* Progress gradient */}
              <linearGradient id={`progress-gradient-${config.id}`} x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#3b82f6" />
                <stop offset="50%" stopColor="#8b5cf6" />
                <stop offset="100%" stopColor={getGradientColor()} />
              </linearGradient>

              {/* Glow filter */}
              <filter id={`glow-${config.id}`}>
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            {/* Track */}
            <rect
              x="10"
              y="15"
              width="calc(100% - 20px)"
              height="10"
              rx="5"
              fill={`url(#track-gradient-${config.id})`}
              className="transition-all duration-200"
            />

            {/* Progress */}
            <rect
              x="10"
              y="15"
              width={`calc((100% - 20px) * ${percentage / 100})`}
              height="10"
              rx="5"
              fill={`url(#progress-gradient-${config.id})`}
              className="transition-all duration-200"
            />

            {/* Marks */}
            {marks.map((mark) => {
              const markPercentage = ((mark.value - min) / (max - min)) * 100
              return (
                <g key={mark.value}>
                  <line
                    x1={`calc(10px + (100% - 20px) * ${markPercentage / 100})`}
                    y1="12"
                    x2={`calc(10px + (100% - 20px) * ${markPercentage / 100})`}
                    y2="28"
                    stroke="#64748b"
                    strokeWidth="1"
                  />
                  <text
                    x={`calc(10px + (100% - 20px) * ${markPercentage / 100})`}
                    y="35"
                    textAnchor="middle"
                    className="text-xs fill-muted-foreground"
                  >
                    {mark.label}
                  </text>
                </g>
              )
            })}

            {/* Handle */}
            <circle
              cx={`calc(10px + (100% - 20px) * ${percentage / 100})`}
              cy="20"
              r={isDragging ? "8" : isHovering ? "7" : "6"}
              fill="white"
              stroke={getGradientColor()}
              strokeWidth="3"
              filter={isDragging ? `url(#glow-${config.id})` : undefined}
              className="transition-all duration-200 drop-shadow-md"
            />

            {/* Value indicator */}
            {(isDragging || isHovering) && (
              <g>
                <rect
                  x={`calc(10px + (100% - 20px) * ${percentage / 100} - 15px)`}
                  y="0"
                  width="30"
                  height="12"
                  rx="6"
                  fill="black"
                  fillOpacity="0.8"
                />
                <text
                  x={`calc(10px + (100% - 20px) * ${percentage / 100})`}
                  y="8"
                  textAnchor="middle"
                  className="text-xs fill-white font-medium"
                >
                  {localValue}{unit}
                </text>
              </g>
            )}
          </svg>
        </div>

        {/* Value input and range display */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Target className="h-3 w-3" />
            <span>{min}{unit}</span>
          </div>

          {showValue && (
            <div className="flex items-center gap-1">
              <Input
                type="number"
                value={localValue}
                onChange={(e) => {
                  const newValue = Number(e.target.value)
                  if (!isNaN(newValue) && newValue >= min && newValue <= max) {
                    setLocalValue(newValue)
                    onChange(newValue)
                  }
                }}
                min={min}
                max={max}
                step={step}
                disabled={disabled || config.disabled}
                className="w-16 h-6 text-xs text-center"
              />
              {unit && (
                <Badge variant="outline" className="text-xs h-4 px-1">
                  {unit}
                </Badge>
              )}
            </div>
          )}

          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span>{max}{unit}</span>
            <Target className="h-3 w-3" />
          </div>
        </div>
      </div>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Quick values */}
      {config.quickValues && (
        <div className="flex flex-wrap gap-1">
          {config.quickValues.map((quickValue: number) => (
            <Button
              key={quickValue}
              variant={localValue === quickValue ? 'default' : 'outline'}
              size="sm"
              onClick={() => {
                setLocalValue(quickValue)
                onChange(quickValue)
              }}
              className="h-6 px-2 text-xs"
            >
              {quickValue}{unit}
            </Button>
          ))}
        </div>
      )}
    </div>
  )
}
