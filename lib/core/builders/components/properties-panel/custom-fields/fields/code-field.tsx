'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import CodeMirror from '@uiw/react-codemirror'
import { javascript } from '@codemirror/lang-javascript'
import { html } from '@codemirror/lang-html'
import { css } from '@codemirror/lang-css'
import { json } from '@codemirror/lang-json'
import { python } from '@codemirror/lang-python'
import { php } from '@codemirror/lang-php'
import { sql } from '@codemirror/lang-sql'
import { xml } from '@codemirror/lang-xml'
import { markdown } from '@codemirror/lang-markdown'
import { EditorView } from '@codemirror/view'
import { vscodeDark } from '@uiw/codemirror-theme-vscode'
import { 
  Code2, 
  RotateCcw, 
  AlertTriangle,
  Download,
  Copy,
  Maximize2,
  Minimize2
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface CodeValue {
  code: string
  language: string
  theme: 'light' | 'dark'
  lineNumbers: boolean
  wordWrap: boolean
  tabSize: number
}

const LANGUAGE_OPTIONS = [
  { value: 'javascript', label: 'JavaScript', extension: [javascript()] },
  { value: 'typescript', label: 'TypeScript', extension: [javascript({ typescript: true })] },
  { value: 'html', label: 'HTML', extension: [html()] },
  { value: 'css', label: 'CSS', extension: [css()] },
  { value: 'json', label: 'JSON', extension: [json()] },
  { value: 'jsx', label: 'JSX', extension: [javascript({ jsx: true })] },
  { value: 'tsx', label: 'TSX', extension: [javascript({ jsx: true, typescript: true })] },
  { value: 'scss', label: 'SCSS', extension: [css()] },
  { value: 'xml', label: 'XML', extension: [xml()] },
  { value: 'yaml', label: 'YAML', extension: [] },
  { value: 'markdown', label: 'Markdown', extension: [markdown()] },
  { value: 'sql', label: 'SQL', extension: [sql()] },
  { value: 'python', label: 'Python', extension: [python()] },
  { value: 'php', label: 'PHP', extension: [php()] },
  { value: 'bash', label: 'Bash', extension: [] }
]

export function CodeField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<CodeValue>({
    ...(value as CodeValue || {}),
    code: (value as CodeValue)?.code || (value as string) || '',
    language: (value as CodeValue)?.language || 'javascript',
    theme: (value as CodeValue)?.theme || 'light',
    lineNumbers: (value as CodeValue)?.lineNumbers ?? true,
    wordWrap: (value as CodeValue)?.wordWrap ?? true,
    tabSize: (value as CodeValue)?.tabSize || 2
  })
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)

  useEffect(() => {
    setLocalValue({
      ...(value as CodeValue || {}),
      code: (value as CodeValue)?.code || (value as string) || '',
      language: (value as CodeValue)?.language || 'javascript',
      theme: (value as CodeValue)?.theme || 'light',
      lineNumbers: (value as CodeValue)?.lineNumbers ?? true,
      wordWrap: (value as CodeValue)?.wordWrap ?? true,
      tabSize: (value as CodeValue)?.tabSize || 2
    })
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue.code)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateCode = (updates: Partial<CodeValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: CodeValue = {
      ...(config.defaultValue as CodeValue || {}),
      code: (config.defaultValue as CodeValue)?.code || '',
      language: (config.defaultValue as CodeValue)?.language || 'javascript',
      theme: (config.defaultValue as CodeValue)?.theme || 'light',
      lineNumbers: (config.defaultValue as CodeValue)?.lineNumbers ?? true,
      wordWrap: (config.defaultValue as CodeValue)?.wordWrap ?? true,
      tabSize: (config.defaultValue as CodeValue)?.tabSize || 2
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const getFileExtension = (language: string): string => {
    const extensions: Record<string, string> = {
      javascript: 'js',
      typescript: 'ts',
      jsx: 'jsx',
      tsx: 'tsx',
      html: 'html',
      css: 'css',
      scss: 'scss',
      json: 'json',
      xml: 'xml',
      yaml: 'yml',
      markdown: 'md',
      sql: 'sql',
      python: 'py',
      php: 'php',
      bash: 'sh'
    }
    return extensions[language] || 'txt'
  }

  const downloadCode = () => {
    const extension = getFileExtension(localValue.language)
    const filename = `code.${extension}`
    
    const blob = new Blob([localValue.code], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(localValue.code)
    } catch (err) {
      console.error('Failed to copy code:', err)
    }
  }

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])
  const lineCount = localValue.code.split('\n').length
  const charCount = localValue.code.length

  const currentLanguage = LANGUAGE_OPTIONS.find(lang => lang.value === localValue.language)
  const extensions = currentLanguage?.extension || []

  const editorTheme = localValue.theme === 'dark' ? vscodeDark : EditorView.theme({
    '&': {
      backgroundColor: 'transparent !important'
    }
  })

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Code2 className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        
        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs h-4 px-1">
            {localValue.language}
          </Badge>
          <Badge variant="outline" className="text-xs h-4 px-1">
            {lineCount} lines
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Code Editor */}
      <div className="space-y-3">
        {/* Settings */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Select
              value={localValue.language}
              onValueChange={(value) => updateCode({ language: value })}
              disabled={disabled}
            >
              <SelectTrigger className="w-32 h-6 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {LANGUAGE_OPTIONS.map((lang) => (
                  <SelectItem key={lang.value} value={lang.value} className="text-xs">
                    {lang.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={localValue.theme}
              onValueChange={(value) => updateCode({ theme: value as 'light' | 'dark' })}
              disabled={disabled}
            >
              <SelectTrigger className="w-20 h-6 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light" className="text-xs">Light</SelectItem>
                <SelectItem value="dark" className="text-xs">Dark</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={copyToClipboard}
              disabled={disabled || !localValue.code}
              className="h-6 w-6 p-0"
              title="Copy code"
            >
              <Copy className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={downloadCode}
              disabled={disabled || !localValue.code}
              className="h-6 w-6 p-0"
              title="Download code"
            >
              <Download className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
              disabled={disabled}
              className="h-6 w-6 p-0"
              title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
            >
              {isFullscreen ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {/* Editor Container */}
        <div className={cn(
          'border rounded-lg overflow-hidden',
          localValue.theme === 'dark' ? 'bg-gray-900' : 'bg-background',
          isFullscreen && 'fixed inset-4 z-50 shadow-2xl'
        )}>
          {isFullscreen && (
            <div className="flex items-center justify-between p-2 border-b bg-muted/50">
              <div className="flex items-center gap-2">
                <Code2 className="h-4 w-4" />
                <span className="text-sm font-medium">Code Editor</span>
                <Badge variant="outline" className="text-xs">
                  {localValue.language}
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFullscreen(false)}
                className="h-6 w-6 p-0"
              >
                <Minimize2 className="h-3 w-3" />
              </Button>
            </div>
          )}

          <div className="relative">
            <CodeMirror
              value={localValue.code}
              onChange={(value) => updateCode({ code: value })}
              theme={editorTheme}
              extensions={[
                ...extensions,
                localValue.wordWrap ? EditorView.lineWrapping : []
              ]}
              basicSetup={{
                lineNumbers: localValue.lineNumbers,
                tabSize: localValue.tabSize,
                foldGutter: true,
                dropCursor: true,
                allowMultipleSelections: true,
                indentOnInput: true,
                bracketMatching: true,
                closeBrackets: true,
                autocompletion: true,
                rectangularSelection: true,
                crosshairCursor: true,
                highlightActiveLine: true,
                highlightSelectionMatches: true,
                closeBracketsKeymap: true,
                searchKeymap: true,
                foldKeymap: true,
                completionKeymap: true,
                lintKeymap: true
              }}
              height={isFullscreen ? 'calc(100vh - 200px)' : '200px'}
              className={cn(
                'font-mono text-sm',
                localValue.theme === 'dark' ? 'bg-gray-900 text-gray-100' : 'bg-background'
              )}
              editable={!disabled}
            />
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-2 border-t bg-muted/20 text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>{lineCount} lines</span>
              <span>{charCount} characters</span>
              <span>Tab size: {localValue.tabSize}</span>
            </div>
            
            <div className="flex items-center gap-2">
              <label className="flex items-center space-x-1">
                <input
                  type="checkbox"
                  checked={localValue.lineNumbers}
                  onChange={(e) => updateCode({ lineNumbers: e.target.checked })}
                  disabled={disabled}
                  className="rounded"
                />
                <span>Line numbers</span>
              </label>
              <label className="flex items-center space-x-1">
                <input
                  type="checkbox"
                  checked={localValue.wordWrap}
                  onChange={(e) => updateCode({ wordWrap: e.target.checked })}
                  disabled={disabled}
                  className="rounded"
                />
                <span>Word wrap</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
