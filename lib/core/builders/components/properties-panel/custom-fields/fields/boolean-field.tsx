'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { RotateCcw, AlertTriangle, Check, X } from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

export function BooleanField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState(Boolean(value))
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(Boolean(value))
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const handleChange = (newValue: boolean) => {
    setLocalValue(newValue)
    onChange(newValue)
  }

  const handleReset = () => {
    const defaultValue = Boolean(config.defaultValue)
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])
  const variant = config.variant || 'switch' // switch, checkbox, radio, button

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label and controls */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium">
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        
        {/* Reset button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          className="h-5 w-5 p-0"
          title="Reset to default"
        >
          <RotateCcw className="h-3 w-3" />
        </Button>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Boolean input based on variant */}
      <div className="space-y-2">
        {variant === 'switch' && (
          <div className="flex items-center space-x-2">
            <Switch
              id={config.id}
              checked={localValue}
              onCheckedChange={handleChange}
              disabled={disabled || config.disabled}
              className={cn(config.className)}
            />
            <Label htmlFor={config.id} className="text-xs">
              {localValue ? (config.trueLabel || 'Enabled') : (config.falseLabel || 'Disabled')}
            </Label>
          </div>
        )}

        {variant === 'checkbox' && (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={config.id}
              checked={localValue}
              onCheckedChange={handleChange}
              disabled={disabled || config.disabled}
              className={cn(config.className)}
            />
            <Label htmlFor={config.id} className="text-xs">
              {config.checkboxLabel || config.label}
            </Label>
          </div>
        )}

        {variant === 'radio' && (
          <RadioGroup
            value={localValue ? 'true' : 'false'}
            onValueChange={(value) => handleChange(value === 'true')}
            disabled={disabled || config.disabled}
            className={cn('flex gap-4', config.className)}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="true" id={`${config.id}-true`} />
              <Label htmlFor={`${config.id}-true`} className="text-xs">
                {config.trueLabel || 'Yes'}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="false" id={`${config.id}-false`} />
              <Label htmlFor={`${config.id}-false`} className="text-xs">
                {config.falseLabel || 'No'}
              </Label>
            </div>
          </RadioGroup>
        )}

        {variant === 'button' && (
          <div className="flex gap-2">
            <Button
              variant={localValue ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleChange(true)}
              disabled={disabled || config.disabled}
              className={cn('flex-1 text-xs', config.className)}
            >
              <Check className="h-3 w-3 mr-1" />
              {config.trueLabel || 'Yes'}
            </Button>
            <Button
              variant={!localValue ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleChange(false)}
              disabled={disabled || config.disabled}
              className={cn('flex-1 text-xs', config.className)}
            >
              <X className="h-3 w-3 mr-1" />
              {config.falseLabel || 'No'}
            </Button>
          </div>
        )}

        {variant === 'toggle' && (
          <Button
            variant={localValue ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleChange(!localValue)}
            disabled={disabled || config.disabled}
            className={cn('w-full text-xs', config.className)}
          >
            {localValue ? (
              <>
                <Check className="h-3 w-3 mr-1" />
                {config.trueLabel || 'Enabled'}
              </>
            ) : (
              <>
                <X className="h-3 w-3 mr-1" />
                {config.falseLabel || 'Disabled'}
              </>
            )}
          </Button>
        )}
      </div>

      {/* Current value indicator */}
      {config.showValue && (
        <div className="text-xs text-muted-foreground">
          Current value: <code className="bg-muted px-1 rounded">{String(localValue)}</code>
        </div>
      )}

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Help text */}
      {config.helpText && !hasError && (
        <p className="text-xs text-muted-foreground">{config.helpText}</p>
      )}
    </div>
  )
}

// Specialized boolean field variants
export function SwitchField(props: CustomFieldProps) {
  return (
    <BooleanField
      {...props}
      config={{
        ...props.config,
        variant: 'switch'
      }}
    />
  )
}

export function CheckboxField(props: CustomFieldProps) {
  return (
    <BooleanField
      {...props}
      config={{
        ...props.config,
        variant: 'checkbox'
      }}
    />
  )
}

export function RadioBooleanField(props: CustomFieldProps) {
  return (
    <BooleanField
      {...props}
      config={{
        ...props.config,
        variant: 'radio'
      }}
    />
  )
}

export function ToggleButtonField(props: CustomFieldProps) {
  return (
    <BooleanField
      {...props}
      config={{
        ...props.config,
        variant: 'button'
      }}
    />
  )
}

export function ToggleField(props: CustomFieldProps) {
  return (
    <BooleanField
      {...props}
      config={{
        ...props.config,
        variant: 'toggle'
      }}
    />
  )
}
