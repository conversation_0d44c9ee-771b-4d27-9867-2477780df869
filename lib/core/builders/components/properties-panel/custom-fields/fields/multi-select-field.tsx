'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { 
  Check, 
  X, 
  Search, 
  RotateCcw, 
  AlertTriangle,
  Plus,
  Grid3X3,
  List,
  Sparkles,
  Filter
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

export function MultiSelectField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<any[]>(Array.isArray(value) ? value : [])
  const [isOpen, setIsOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(Array.isArray(value) ? value : [])
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const handleToggleOption = (optionValue: any) => {
    const newValue = localValue.includes(optionValue)
      ? localValue.filter(v => v !== optionValue)
      : [...localValue, optionValue]
    
    setLocalValue(newValue)
    onChange(newValue)
  }

  const handleClearAll = () => {
    setLocalValue([])
    onChange([])
  }

  const handleSelectAll = () => {
    const allValues = filteredOptions.map(option => option.value)
    setLocalValue(allValues)
    onChange(allValues)
  }

  const handleReset = () => {
    const defaultValue = Array.isArray(config.defaultValue) ? config.defaultValue : []
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const options = config.options || []
  const filteredOptions = searchValue
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchValue.toLowerCase()) ||
        String(option.value).toLowerCase().includes(searchValue.toLowerCase())
      )
    : options

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  // Generate SVG pattern for selected items visualization
  const generateSelectionPattern = () => {
    const selectedCount = localValue.length
    const totalCount = options.length
    const percentage = totalCount > 0 ? (selectedCount / totalCount) * 100 : 0
    
    return (
      <svg width="100%" height="20" className="mb-2">
        <defs>
          <linearGradient id={`selection-gradient-${config.id}`} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="50%" stopColor="#8b5cf6" />
            <stop offset="100%" stopColor="#ec4899" />
          </linearGradient>
          
          <pattern id={`dots-${config.id}`} x="0" y="0" width="4" height="4" patternUnits="userSpaceOnUse">
            <circle cx="2" cy="2" r="1" fill="#e2e8f0" />
          </pattern>
        </defs>
        
        {/* Background */}
        <rect
          width="100%"
          height="20"
          rx="10"
          fill={`url(#dots-${config.id})`}
          stroke="#e2e8f0"
          strokeWidth="1"
        />
        
        {/* Progress */}
        <rect
          width={`${percentage}%`}
          height="20"
          rx="10"
          fill={`url(#selection-gradient-${config.id})`}
          className="transition-all duration-300"
        />
        
        {/* Selection indicators */}
        {options.slice(0, 10).map((option, index) => {
          const isSelected = localValue.includes(option.value)
          const x = (index / Math.min(options.length, 10)) * 100
          
          return (
            <circle
              key={option.value}
              cx={`${x + 5}%`}
              cy="10"
              r="3"
              fill={isSelected ? "white" : "transparent"}
              stroke={isSelected ? "#3b82f6" : "#cbd5e1"}
              strokeWidth="1"
              className="transition-all duration-200"
            />
          )
        })}
        
        {/* Count text */}
        <text
          x="50%"
          y="14"
          textAnchor="middle"
          className="text-xs fill-white font-medium"
          style={{ textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}
        >
          {selectedCount} / {totalCount}
        </text>
      </svg>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Sparkles className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        
        {/* Actions */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            className="h-5 w-5 p-0"
            title={`Switch to ${viewMode === 'grid' ? 'list' : 'grid'} view`}
          >
            {viewMode === 'grid' ? <List className="h-3 w-3" /> : <Grid3X3 className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Selection visualization */}
      {generateSelectionPattern()}

      {/* Selected items display */}
      {localValue.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {localValue.map((selectedValue) => {
            const option = options.find(opt => opt.value === selectedValue)
            return (
              <Badge
                key={selectedValue}
                variant="secondary"
                className="text-xs flex items-center gap-1"
              >
                {option?.icon}
                {option?.label || selectedValue}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleToggleOption(selectedValue)}
                  className="h-3 w-3 p-0 ml-1 hover:bg-destructive hover:text-destructive-foreground"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )
          })}
        </div>
      )}

      {/* Dropdown trigger */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between text-xs h-8',
              localValue.length === 0 && 'text-muted-foreground',
              hasError && 'border-destructive',
              config.className
            )}
            disabled={disabled || config.disabled}
          >
            <div className="flex items-center gap-2">
              <Filter className="h-3 w-3" />
              <span>
                {localValue.length === 0 
                  ? (config.placeholder || 'Select options...')
                  : `${localValue.length} selected`
                }
              </span>
            </div>
            <Plus className="h-3 w-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-3 border-b">
            {/* Search */}
            <div className="flex items-center gap-2 mb-2">
              <Search className="h-3 w-3 text-muted-foreground" />
              <Input
                placeholder="Search options..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="text-xs h-6 border-0 focus-visible:ring-0"
              />
            </div>
            
            {/* Bulk actions */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                className="text-xs h-6 flex-1"
              >
                Select All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAll}
                className="text-xs h-6 flex-1"
              >
                Clear All
              </Button>
            </div>
          </div>

          {/* Options */}
          <div className="max-h-64 overflow-auto p-2">
            {filteredOptions.length === 0 ? (
              <div className="text-center py-6 text-xs text-muted-foreground">
                No options found
              </div>
            ) : (
              <div className={cn(
                viewMode === 'grid' 
                  ? 'grid grid-cols-2 gap-1' 
                  : 'space-y-1'
              )}>
                {filteredOptions.map((option) => {
                  const isSelected = localValue.includes(option.value)
                  
                  return (
                    <Button
                      key={String(option.value)}
                      variant={isSelected ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => handleToggleOption(option.value)}
                      className={cn(
                        'justify-start text-xs h-8 relative',
                        viewMode === 'grid' && 'flex-col h-12'
                      )}
                    >
                      {/* Selection indicator */}
                      <div className={cn(
                        'absolute top-1 right-1 w-3 h-3 rounded-full border transition-all',
                        isSelected 
                          ? 'bg-primary border-primary' 
                          : 'border-muted-foreground'
                      )}>
                        {isSelected && (
                          <Check className="h-2 w-2 text-primary-foreground absolute top-0.5 left-0.5" />
                        )}
                      </div>
                      
                      <div className={cn(
                        'flex items-center gap-2',
                        viewMode === 'grid' && 'flex-col'
                      )}>
                        {option.icon}
                        <div className={cn(
                          'text-left',
                          viewMode === 'grid' && 'text-center'
                        )}>
                          <div className="font-medium">{option.label}</div>
                          {(option as any).description && (
                            <div className="text-xs text-muted-foreground">
                              {(option as any).description}
                            </div>
                          )}
                        </div>
                      </div>
                    </Button>
                  )
                })}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
