'use client'

import { FieldConfig, FieldValue, FieldValidation } from './types'

// Validation result interface
export interface ValidationResult {
  isValid: boolean
  message?: string
}

// Main validation function
export function validateField(config: FieldConfig, value: FieldValue): ValidationResult {
  const validation = config.validation
  
  if (!validation) {
    return { isValid: true }
  }

  // Check required validation
  if (validation.required && (value === null || value === undefined || value === '')) {
    return {
      isValid: false,
      message: validation.message || `${config.label} is required`
    }
  }

  // Skip other validations if value is empty and not required
  if (value === null || value === undefined || value === '') {
    return { isValid: true }
  }

  // Type-specific validations
  switch (config.type) {
    case 'text':
    case 'textarea':
    case 'richtext':
      return validateStringField(config, value as string, validation)
    
    case 'number':
    case 'range':
      return validateNumberField(config, value as number, validation)
    
    case 'email':
      return validateEmailField(config, value as string, validation)
    
    case 'url':
      return validateUrlField(config, value as string, validation)
    
    case 'color':
      return validateColorField(config, value as string, validation)
    
    case 'date':
    case 'datetime':
      return validateDateField(config, value as string, validation)
    
    case 'file':
    case 'image':
      return validateFileField(config, value, validation)
    
    case 'select':
    case 'multi-select':
      return validateSelectField(config, value, validation)
    
    case 'array':
      return validateArrayField(config, value as any[], validation)
    
    default:
      return validateGenericField(config, value, validation)
  }
}

// String field validation
function validateStringField(
  config: FieldConfig,
  value: string,
  validation: FieldValidation
): ValidationResult {
  // Length validations
  if (validation.minLength !== undefined && value.length < validation.minLength) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be at least ${validation.minLength} characters`
    }
  }

  if (validation.maxLength !== undefined && value.length > validation.maxLength) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be no more than ${validation.maxLength} characters`
    }
  }

  // Pattern validation
  if (validation.pattern && !validation.pattern.test(value)) {
    return {
      isValid: false,
      message: validation.message || `${config.label} format is invalid`
    }
  }

  return { isValid: true }
}

// Number field validation
function validateNumberField(
  config: FieldConfig,
  value: number,
  validation: FieldValidation
): ValidationResult {
  // Convert to number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value) : value

  if (isNaN(numValue)) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be a valid number`
    }
  }

  // Min/max validations
  if (validation.min !== undefined && numValue < validation.min) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be at least ${validation.min}`
    }
  }

  if (validation.max !== undefined && numValue > validation.max) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be no more than ${validation.max}`
    }
  }

  return { isValid: true }
}

// Email field validation
function validateEmailField(
  config: FieldConfig,
  value: string,
  validation: FieldValidation
): ValidationResult {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  
  if (!emailRegex.test(value)) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be a valid email address`
    }
  }

  return validateStringField(config, value, validation)
}

// URL field validation
function validateUrlField(
  config: FieldConfig,
  value: string,
  validation: FieldValidation
): ValidationResult {
  try {
    new URL(value)
  } catch {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be a valid URL`
    }
  }

  return validateStringField(config, value, validation)
}

// Color field validation
function validateColorField(
  config: FieldConfig,
  value: string,
  validation: FieldValidation
): ValidationResult {
  // Hex color validation
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  // RGB/RGBA validation
  const rgbRegex = /^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(?:,\s*[\d.]+\s*)?\)$/
  // HSL/HSLA validation
  const hslRegex = /^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(?:,\s*[\d.]+\s*)?\)$/

  if (!hexRegex.test(value) && !rgbRegex.test(value) && !hslRegex.test(value)) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be a valid color value`
    }
  }

  return { isValid: true }
}

// Date field validation
function validateDateField(
  config: FieldConfig,
  value: string,
  validation: FieldValidation
): ValidationResult {
  const date = new Date(value)
  
  if (isNaN(date.getTime())) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be a valid date`
    }
  }

  return { isValid: true }
}

// File field validation
function validateFileField(
  config: FieldConfig,
  value: any,
  validation: FieldValidation
): ValidationResult {
  // This would typically validate file size, type, etc.
  // Implementation depends on how files are handled in your app
  return { isValid: true }
}

// Select field validation
function validateSelectField(
  config: FieldConfig,
  value: any,
  validation: FieldValidation
): ValidationResult {
  if (config.options) {
    const validValues = config.options.map(option => option.value)
    
    if (Array.isArray(value)) {
      // Multi-select validation
      const invalidValues = value.filter(v => !validValues.includes(v))
      if (invalidValues.length > 0) {
        return {
          isValid: false,
          message: validation.message || `${config.label} contains invalid options`
        }
      }
    } else {
      // Single select validation
      if (!validValues.includes(value)) {
        return {
          isValid: false,
          message: validation.message || `${config.label} must be a valid option`
        }
      }
    }
  }

  return { isValid: true }
}

// Array field validation
function validateArrayField(
  config: FieldConfig,
  value: any[],
  validation: FieldValidation
): ValidationResult {
  if (!Array.isArray(value)) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must be an array`
    }
  }

  // Length validations
  if (validation.minLength !== undefined && value.length < validation.minLength) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must have at least ${validation.minLength} items`
    }
  }

  if (validation.maxLength !== undefined && value.length > validation.maxLength) {
    return {
      isValid: false,
      message: validation.message || `${config.label} must have no more than ${validation.maxLength} items`
    }
  }

  return { isValid: true }
}

// Generic field validation (fallback)
function validateGenericField(
  config: FieldConfig,
  value: FieldValue,
  validation: FieldValidation
): ValidationResult {
  // Custom validation function
  if (validation.custom) {
    const customResult = validation.custom(value)
    if (customResult) {
      return {
        isValid: false,
        message: customResult
      }
    }
  }

  return { isValid: true }
}

// Validate multiple fields
export function validateFields(
  configs: FieldConfig[],
  values: Record<string, FieldValue>
): Record<string, ValidationResult> {
  const results: Record<string, ValidationResult> = {}

  configs.forEach(config => {
    results[config.id] = validateField(config, values[config.id])
  })

  return results
}

// Check if all fields are valid
export function areAllFieldsValid(
  validationResults: Record<string, ValidationResult>
): boolean {
  return Object.values(validationResults).every(result => result.isValid)
}

// Get validation errors
export function getValidationErrors(
  validationResults: Record<string, ValidationResult>
): Record<string, string> {
  const errors: Record<string, string> = {}

  Object.entries(validationResults).forEach(([fieldId, result]) => {
    if (!result.isValid && result.message) {
      errors[fieldId] = result.message
    }
  })

  return errors
}
