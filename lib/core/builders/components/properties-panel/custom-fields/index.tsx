'use client'

// Export all custom field components
export { CustomFieldRenderer } from './field-renderer'
export { FieldGroup } from './field-group'
export { FieldSection } from './field-section'

// Basic field types
export { TextField } from './fields/text-field'
export { NumberField } from './fields/number-field'
export { BooleanField } from './fields/boolean-field'
export { SelectField } from './fields/select-field'
export { MultiSelectField } from './fields/multi-select-field'

// Advanced field types
export { ColorField } from './fields/color-field'
export { RangeField } from './fields/range-field'
export { TextareaField } from './fields/textarea-field'
export { RichTextField } from './fields/rich-text-field'
export { CodeField } from './fields/code-field'
export { ImageField } from './fields/image-field'
export { FileField } from './fields/file-field'
export { DateField } from './fields/date-field'
export { TimeField } from './fields/time-field'
export { DateTimeField } from './fields/date-time-field'

// Specialized field types
export { IconField } from './fields/icon-field'
export { FontField } from './fields/font-field'
export { SpacingField } from './fields/spacing-field'
export { BorderField } from './fields/border-field'
export { ShadowField } from './fields/shadow-field'
export { GradientField } from './fields/gradient-field'
export { AnimationField } from './fields/animation-field'
export { LinkField } from './fields/link-field'
export { MediaField } from './fields/media-field'

// Composite field types
export { RepeaterField } from './fields/repeater-field'
export { ObjectField } from './fields/object-field'
export { ConditionalField } from './fields/conditional-field'
export { TabsField } from './fields/tabs-field'
export { AccordionField } from './fields/accordion-field'

// Field validation and utilities
export { validateField } from './validation'
export { getFieldDefaultValue } from './defaults'
export { createFieldSchema } from './schema'

// Field types and interfaces
export type {
  FieldConfig,
  FieldValue,
  FieldValidation,
  FieldSchema,
  CustomFieldProps,
  FieldGroupConfig,
  FieldSectionConfig
} from './types'
