@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base font is handled by <PERSON><PERSON><PERSON> through the sans font family */

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
  --background: 223.8136 0.0005% 98.6829%;
  --foreground: 0 0% 0%;
  --card: 223.8136 -172.5242% 100.0000%;
  --card-foreground: 0 0% 0%;
  --popover: 223.8136 0.0005% 98.6829%;
  --popover-foreground: 0 0% 0%;
  --primary: 0 0% 0%;
  --primary-foreground: 223.8136 -172.5242% 100.0000%;
  --secondary: 223.8136 0.0001% 92.1478%;
  --secondary-foreground: 0 0% 0%;
  --muted: 223.8136 0.0002% 96.0587%;
  --muted-foreground: 223.8136 0.0000% 32.3067%;
  --accent: 223.8136 0.0001% 92.1478%;
  --accent-foreground: 0 0% 0%;
  --destructive: 358.4334 74.9120% 59.7455%;
  --destructive-foreground: 223.8136 -172.5242% 100.0000%;
  --border: 223.8136 0.0001% 89.5577%;
  --input: 223.8136 0.0001% 92.1478%;
  --ring: 0 0% 0%;
  --chart-1: 40.6655 100.2361% 50.9228%;
  --chart-2: 223.7490 85.9924% 55.8092%;
  --chart-3: 223.8136 0.0000% 64.4710%;
  --chart-4: 223.8136 0.0001% 89.5577%;
  --chart-5: 223.8136 0.0000% 45.6078%;
  --sidebar: 223.8136 0.0005% 98.6829%;
  --sidebar-foreground: 0 0% 0%;
  --sidebar-primary: 0 0% 0%;
  --sidebar-primary-foreground: 223.8136 -172.5242% 100.0000%;
  --sidebar-accent: 223.8136 0.0001% 92.1478%;
  --sidebar-accent-foreground: 0 0% 0%;
  --sidebar-border: 223.8136 0.0001% 92.1478%;
  --sidebar-ring: 0 0% 0%;
  --font-sans: Outfit, sans-serif;
  --font-serif: Plus Jakarta Sans, sans-serif;
  --font-mono: Geist Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 0px 1px 2px -1px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 2px -1px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 1px 2px -2px hsl(0 0% 0% / 0.18);
  --shadow: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 1px 2px -2px hsl(0 0% 0% / 0.18);
  --shadow-md: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 2px 4px -2px hsl(0 0% 0% / 0.18);
  --shadow-lg: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 4px 6px -2px hsl(0 0% 0% / 0.18);
  --shadow-xl: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 8px 10px -2px hsl(0 0% 0% / 0.18);
  --shadow-2xl: 0px 1px 2px -1px hsl(0 0% 0% / 0.45);
  --spacing: 0.2rem;
}

.dark {
  --background: 0 0% 0%;
  --foreground: 223.8136 -172.5242% 100.0000%;
  --card: 223.8136 0.0000% 3.5452%;
  --card-foreground: 223.8136 -172.5242% 100.0000%;
  --popover: 223.8136 0.0000% 6.8692%;
  --popover-foreground: 223.8136 -172.5242% 100.0000%;
  --primary: 223.8136 -172.5242% 100.0000%;
  --primary-foreground: 0 0% 0%;
  --secondary: 223.8136 0.0000% 13.1499%;
  --secondary-foreground: 223.8136 -172.5242% 100.0000%;
  --muted: 223.8136 0.0000% 11.3040%;
  --muted-foreground: 223.8136 0.0000% 64.4710%;
  --accent: 223.8136 0.0000% 19.8916%;
  --accent-foreground: 223.8136 -172.5242% 100.0000%;
  --destructive: 359.9132 100.2494% 67.8807%;
  --destructive-foreground: 0 0% 0%;
  --border: 223.8136 0.0000% 14.0871%;
  --input: 223.8136 0.0000% 19.8916%;
  --ring: 223.8136 0.0000% 64.4710%;
  --chart-1: 40.6655 100.2361% 50.9228%;
  --chart-2: 218.1624 90.0354% 55.1618%;
  --chart-3: 223.8136 0.0000% 45.6078%;
  --chart-4: 223.8136 0.0000% 32.3067%;
  --chart-5: 223.8136 0.0001% 89.5577%;
  --sidebar: 223.8136 0.0000% 6.8692%;
  --sidebar-foreground: 223.8136 -172.5242% 100.0000%;
  --sidebar-primary: 223.8136 -172.5242% 100.0000%;
  --sidebar-primary-foreground: 0 0% 0%;
  --sidebar-accent: 223.8136 0.0000% 19.8916%;
  --sidebar-accent-foreground: 223.8136 -172.5242% 100.0000%;
  --sidebar-border: 223.8136 0.0000% 19.8916%;
  --sidebar-ring: 223.8136 0.0000% 64.4710%;
  --font-sans: Outfit, sans-serif;
  --font-serif: Plus Jakarta Sans, sans-serif;
  --font-mono: Geist Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 0px 1px 2px -1px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 2px -1px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 1px 2px -2px hsl(0 0% 0% / 0.18);
  --shadow: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 1px 2px -2px hsl(0 0% 0% / 0.18);
  --shadow-md: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 2px 4px -2px hsl(0 0% 0% / 0.18);
  --shadow-lg: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 4px 6px -2px hsl(0 0% 0% / 0.18);
  --shadow-xl: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 8px 10px -2px hsl(0 0% 0% / 0.18);
  --shadow-2xl: 0px 1px 2px -1px hsl(0 0% 0% / 0.45);
}
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Clean Typography - Selfi.co.za inspired */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
  letter-spacing: 0.01em;
  line-height: 1.2;
}

body {
  letter-spacing: 0.005em;
  font-weight: 400;
  line-height: 1.5;
}

/* Clean product titles */
.product-title {
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.3;
  letter-spacing: 0.01em;
}

/* Clean Animations - Selfi.co.za inspired */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.4s ease-out forwards;
}

/* Subtle hover effects */
.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.image-zoom {
  overflow: hidden;
}

.image-zoom img {
  transition: transform 0.6s ease;
}

.image-zoom:hover img {
  transform: scale(1.02);
}

/* Clean Button Styles - Selfi.co.za inspired */
.btn-minimal {
  font-weight: 400;
  letter-spacing: 0.01em;
  transition: all 0.2s ease;
  position: relative;
}

.btn-minimal::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.2s ease;
}

.btn-minimal:hover::after {
  width: 100%;
}

/* Clean Product Card */
.product-card-clean {
  transition: opacity 0.2s ease;
}

.product-card-clean:hover {
  opacity: 0.8;
}

/* Simple Hover Effects */
.hover-underline {
  position: relative;
}

.hover-underline::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.2s ease;
}

.hover-underline:hover::after {
  width: 100%;
}

/* Clean borders */
.border-clean {
  border: 1px solid #f0f0f0;
}

/* Minimal spacing */
.space-clean > * + * {
  margin-top: 0.5rem;
}
