'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  HardDrive,
  Settings,
  Trash2,
  RefreshCw,
  Database,
  Shield,
  Zap,
  AlertTriangle,
  CheckCircle,
  Cloud,
  Server
} from 'lucide-react'
import { formatFileSize } from '@/lib/utils'
import { toast } from 'sonner'

interface StorageStats {
  totalFiles: number
  totalSize: number
  usedSpace: number
  availableSpace: number
  buckets: {
    id: string
    name: string
    fileCount: number
    size: number
  }[]
}

interface MediaSettings {
  maxFileSize: number
  allowedFileTypes: string[]
  autoOptimization: boolean
  compressionQuality: number
  thumbnailSize: number
  enableCDN: boolean
  cacheExpiry: number
}

export default function MediaSettingsPage() {
  const [stats, setStats] = useState<StorageStats | null>(null)
  const [settings, setSettings] = useState<MediaSettings>({
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    autoOptimization: true,
    compressionQuality: 80,
    thumbnailSize: 300,
    enableCDN: true,
    cacheExpiry: 86400 // 24 hours
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [cleaning, setCleaning] = useState(false)

  useEffect(() => {
    loadStorageStats()
    loadSettings()
  }, [])

  const loadStorageStats = async () => {
    try {
      // Mock data for now - in production, this would come from an API
      const mockStats: StorageStats = {
        totalFiles: 1247,
        totalSize: 2.4 * 1024 * 1024 * 1024, // 2.4GB
        usedSpace: 2.4 * 1024 * 1024 * 1024,
        availableSpace: 7.6 * 1024 * 1024 * 1024, // 7.6GB
        buckets: [
          {
            id: 'media-library',
            name: 'Media Library',
            fileCount: 856,
            size: 1.8 * 1024 * 1024 * 1024
          },
          {
            id: 'product-images',
            name: 'Product Images',
            fileCount: 324,
            size: 512 * 1024 * 1024
          },
          {
            id: 'user-avatars',
            name: 'User Avatars',
            fileCount: 67,
            size: 89 * 1024 * 1024
          }
        ]
      }
      setStats(mockStats)
    } catch (error) {
      console.error('Error loading storage stats:', error)
      toast.error('Failed to load storage statistics')
    } finally {
      setLoading(false)
    }
  }

  const loadSettings = async () => {
    try {
      // Mock settings load - in production, this would come from an API
      // Settings are already initialized with defaults
    } catch (error) {
      console.error('Error loading settings:', error)
      toast.error('Failed to load media settings')
    }
  }

  const handleSaveSettings = async () => {
    try {
      setSaving(true)
      
      // Mock save - in production, this would save to an API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('Media settings saved successfully')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  const handleCleanupStorage = async () => {
    if (!confirm('This will remove unused files and optimize storage. Continue?')) {
      return
    }

    try {
      setCleaning(true)
      
      // Mock cleanup - in production, this would call a cleanup API
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      toast.success('Storage cleanup completed successfully')
      loadStorageStats() // Refresh stats
    } catch (error) {
      console.error('Error cleaning storage:', error)
      toast.error('Failed to cleanup storage')
    } finally {
      setCleaning(false)
    }
  }

  const usagePercentage = stats ? (stats.usedSpace / (stats.usedSpace + stats.availableSpace)) * 100 : 0

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Media Settings</h1>
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Media Settings</h1>
          <p className="text-muted-foreground">
            Configure storage settings and manage media files
          </p>
        </div>
        <Button onClick={loadStorageStats} disabled={loading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Storage Overview */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Database className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Total Files</p>
                  <p className="text-2xl font-bold">{stats.totalFiles.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-sm font-medium text-muted-foreground">Storage Used</p>
                <p className="text-2xl font-bold">{formatFileSize(stats.usedSpace)}</p>
                <Progress value={usagePercentage} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {usagePercentage.toFixed(1)}% of total space
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-sm font-medium text-muted-foreground">Available Space</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatFileSize(stats.availableSpace)}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-sm font-medium text-muted-foreground">Total Capacity</p>
                <p className="text-2xl font-bold">
                  {formatFileSize(stats.usedSpace + stats.availableSpace)}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Storage by Bucket */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle>Storage by Bucket</CardTitle>
            <CardDescription>
              Breakdown of storage usage across different buckets
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.buckets.map((bucket) => (
                <div key={bucket.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded">
                      <HardDrive className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{bucket.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {bucket.fileCount} files
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatFileSize(bucket.size)}</p>
                    <p className="text-sm text-muted-foreground">
                      {((bucket.size / stats.totalSize) * 100).toFixed(1)}%
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Media Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Media Configuration</CardTitle>
          <CardDescription>
            Configure file upload and processing settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="maxFileSize">Maximum File Size (MB)</Label>
              <Input
                id="maxFileSize"
                type="number"
                value={Math.round(settings.maxFileSize / (1024 * 1024))}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  maxFileSize: parseInt(e.target.value) * 1024 * 1024
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="thumbnailSize">Thumbnail Size (px)</Label>
              <Input
                id="thumbnailSize"
                type="number"
                value={settings.thumbnailSize}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  thumbnailSize: parseInt(e.target.value)
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="compressionQuality">Image Compression Quality (%)</Label>
              <Input
                id="compressionQuality"
                type="number"
                min="1"
                max="100"
                value={settings.compressionQuality}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  compressionQuality: parseInt(e.target.value)
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cacheExpiry">Cache Expiry (seconds)</Label>
              <Input
                id="cacheExpiry"
                type="number"
                value={settings.cacheExpiry}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  cacheExpiry: parseInt(e.target.value)
                }))}
              />
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Auto Image Optimization</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically optimize images on upload
                </p>
              </div>
              <Switch
                checked={settings.autoOptimization}
                onCheckedChange={(checked) => setSettings(prev => ({
                  ...prev,
                  autoOptimization: checked
                }))}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable CDN</Label>
                <p className="text-sm text-muted-foreground">
                  Use content delivery network for faster file access
                </p>
              </div>
              <Switch
                checked={settings.enableCDN}
                onCheckedChange={(checked) => setSettings(prev => ({
                  ...prev,
                  enableCDN: checked
                }))}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={handleSaveSettings} disabled={saving}>
              {saving ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Settings className="mr-2 h-4 w-4" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Storage Management */}
      <Card>
        <CardHeader>
          <CardTitle>Storage Management</CardTitle>
          <CardDescription>
            Tools for managing and optimizing storage usage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-orange-100 rounded">
                  <Trash2 className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <p className="font-medium">Cleanup Unused Files</p>
                  <p className="text-sm text-muted-foreground">
                    Remove orphaned files and optimize storage
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                onClick={handleCleanupStorage}
                disabled={cleaning}
              >
                {cleaning ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Cleaning...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Start Cleanup
                  </>
                )}
              </Button>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded">
                  <Zap className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="font-medium">Optimize Images</p>
                  <p className="text-sm text-muted-foreground">
                    Compress and optimize existing images
                  </p>
                </div>
              </div>
              <Button variant="outline">
                <Zap className="mr-2 h-4 w-4" />
                Optimize All
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
